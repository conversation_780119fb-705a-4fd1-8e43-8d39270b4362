import * as XLSX from 'xlsx';
import { TaskType } from '../models/TaskConfig';

// Excel配置数据接口
export interface TaskConfigData {
  id: number;
  condition: number;
  type: number;
  describe: string;
  price1: number;
  price2: number;
  diamond: number;
  box: number;
  coin: number;
  item: number;
}

// 验证结果接口
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class ExcelConfigParser {
  /**
   * 解析Excel文件
   * @param filePath Excel文件路径
   * @returns 解析后的任务配置数据
   */
  public parseExcelFile(filePath: string): TaskConfigData[] {
    try {
      // 读取Excel文件
      const workbook = XLSX.readFile(filePath);
      
      // 获取第一个工作表
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) {
        throw new Error('Excel文件中没有找到工作表');
      }
      
      const worksheet = workbook.Sheets[sheetName];
      
      // 将工作表转换为JSON数组
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1,  // 使用数组格式而不是对象格式
        defval: ''  // 空单元格的默认值
      }) as any[][];
      
      // 检查是否有数据
      if (jsonData.length < 4) {
        throw new Error('Excel文件格式不正确，至少需要4行数据（包含标题行）');
      }
      
      // 找到最后一个表头行（包含'id'的行）
      let headerRowIndex = -1;
      for (let i = 0; i < jsonData.length; i++) {
        const row = jsonData[i];
        if (row && row[0] === 'id') {
          headerRowIndex = i; // 不使用break，找到最后一个
        }
      }

      if (headerRowIndex === -1) {
        throw new Error('未找到表头行（包含id列的行）');
      }

      // 从表头行的下一行开始解析数据
      const dataRows = jsonData.slice(headerRowIndex + 1);
      
      const configs: TaskConfigData[] = [];
      
      for (let i = 0; i < dataRows.length; i++) {
        const row = dataRows[i];

        // 跳过空行或表头行
        if (!row || row.length === 0 || !row[0] || row[0] === 'id') {
          continue;
        }
        
        try {
          const rowNumber = headerRowIndex + i + 2; // 实际Excel行号
          const config: TaskConfigData = {
            id: this.parseInteger(row[0], `第${rowNumber}行ID`),
            condition: this.parseInteger(row[1], `第${rowNumber}行condition`, 0),
            type: this.parseInteger(row[2], `第${rowNumber}行type`),
            describe: this.parseString(row[3], `第${rowNumber}行describe`),
            price1: this.parseInteger(row[4], `第${rowNumber}行price1`, 0),
            price2: this.parseInteger(row[5], `第${rowNumber}行price2`, 0),
            diamond: this.parseInteger(row[6], `第${rowNumber}行diamond`, 0),
            box: this.parseInteger(row[7], `第${rowNumber}行box`, 0),
            coin: this.parseInteger(row[8], `第${rowNumber}行coin`, 0),
            item: this.parseInteger(row[9], `第${rowNumber}行item`, 0)
          };
          
          configs.push(config);
        } catch (error: any) {
          const rowNumber = headerRowIndex + i + 2;
          throw new Error(`解析第${rowNumber}行数据失败: ${error.message}`);
        }
      }
      
      return configs;
    } catch (error: any) {
      throw new Error(`解析Excel文件失败: ${error.message}`);
    }
  }

  /**
   * 解析Buffer格式的Excel文件
   * @param buffer Excel文件Buffer
   * @returns 解析后的任务配置数据
   */
  public parseExcelBuffer(buffer: Buffer): TaskConfigData[] {
    try {
      // 从Buffer读取Excel文件
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      
      // 获取第一个工作表
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) {
        throw new Error('Excel文件中没有找到工作表');
      }
      
      const worksheet = workbook.Sheets[sheetName];
      
      // 将工作表转换为JSON数组
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1,
        defval: ''
      }) as any[][];
      
      // 检查是否有数据
      if (jsonData.length < 4) {
        throw new Error('Excel文件格式不正确，至少需要4行数据（包含标题行）');
      }
      
      // 找到最后一个表头行（包含'id'的行）
      let headerRowIndex = -1;
      for (let i = 0; i < jsonData.length; i++) {
        const row = jsonData[i];
        if (row && row[0] === 'id') {
          headerRowIndex = i; // 不使用break，找到最后一个
        }
      }

      if (headerRowIndex === -1) {
        throw new Error('未找到表头行（包含id列的行）');
      }

      // 从表头行的下一行开始解析数据
      const dataRows = jsonData.slice(headerRowIndex + 1);
      
      const configs: TaskConfigData[] = [];
      
      for (let i = 0; i < dataRows.length; i++) {
        const row = dataRows[i];

        // 跳过空行或表头行
        if (!row || row.length === 0 || !row[0] || row[0] === 'id') {
          continue;
        }
        
        try {
          const rowNumber = headerRowIndex + i + 2; // 实际Excel行号
          const config: TaskConfigData = {
            id: this.parseInteger(row[0], `第${rowNumber}行ID`),
            condition: this.parseInteger(row[1], `第${rowNumber}行condition`, 0),
            type: this.parseInteger(row[2], `第${rowNumber}行type`),
            describe: this.parseString(row[3], `第${rowNumber}行describe`),
            price1: this.parseInteger(row[4], `第${rowNumber}行price1`, 0),
            price2: this.parseInteger(row[5], `第${rowNumber}行price2`, 0),
            diamond: this.parseInteger(row[6], `第${rowNumber}行diamond`, 0),
            box: this.parseInteger(row[7], `第${rowNumber}行box`, 0),
            coin: this.parseInteger(row[8], `第${rowNumber}行coin`, 0),
            item: this.parseInteger(row[9], `第${rowNumber}行item`, 0)
          };

          configs.push(config);
        } catch (error: any) {
          const rowNumber = headerRowIndex + i + 2;
          throw new Error(`解析第${rowNumber}行数据失败: ${error.message}`);
        }
      }
      
      return configs;
    } catch (error: any) {
      throw new Error(`解析Excel文件失败: ${error.message}`);
    }
  }

  /**
   * 验证任务配置数据
   * @param configs 任务配置数据数组
   * @returns 验证结果
   */
  public validateTaskConfigs(configs: TaskConfigData[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!configs || configs.length === 0) {
      errors.push('配置数据为空');
      return { errors, warnings, isValid: false };
    }
    
    // 检查ID重复
    const ids = configs.map(c => c.id);
    const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);
    if (duplicateIds.length > 0) {
      errors.push(`任务ID重复：${[...new Set(duplicateIds)].join(', ')}`);
    }
    
    // 逐个验证配置
    configs.forEach((config, index) => {
      const row = index + 4; // Excel中的实际行号
      
      // 验证必填字段
      if (!config.id || config.id <= 0) {
        errors.push(`第${row}行：任务ID必须大于0`);
      }
      
      if (!config.type || ![1, 2, 3, 4].includes(config.type)) {
        errors.push(`第${row}行：任务类型必须为1-4的数值`);
      }
      
      if (!config.describe || config.describe.trim().length === 0) {
        errors.push(`第${row}行：任务描述不能为空`);
      }
      
      // 根据任务类型验证参数
      this.validateTaskTypeParameters(config, row, errors);
      
      // 验证前置任务
      if (config.condition > 0) {
        const prerequisite = configs.find(c => c.id === config.condition);
        if (!prerequisite) {
          errors.push(`第${row}行：前置任务${config.condition}不存在`);
        }
      }
      
      // 验证奖励数值
      if (config.diamond < 0 || config.box < 0 || config.coin < 0 || config.item < 0) {
        errors.push(`第${row}行：奖励数值不能为负数`);
      }
      
      // 检查是否有奖励
      const totalReward = config.diamond + config.box + config.coin + config.item;
      if (totalReward === 0) {
        warnings.push(`第${row}行：任务没有设置任何奖励`);
      }
    });
    
    return {
      errors,
      warnings,
      isValid: errors.length === 0
    };
  }

  /**
   * 根据任务类型验证参数
   */
  private validateTaskTypeParameters(config: TaskConfigData, row: number, errors: string[]): void {
    switch (config.type) {
      case TaskType.UNLOCK_AREA:
        if (config.price1 <= 0) {
          errors.push(`第${row}行：解锁任务price1必须大于0（区域ID）`);
        }
        break;
      case TaskType.UPGRADE_FARM:
        if (config.price1 <= 0 || config.price2 <= 0) {
          errors.push(`第${row}行：升级任务price1和price2必须大于0`);
        }
        break;
      case TaskType.UPGRADE_DELIVERY:
        if (config.price2 <= 0) {
          errors.push(`第${row}行：流水线任务price2必须大于0（目标等级）`);
        }
        break;
      case TaskType.INVITE_FRIENDS:
        if (config.price2 <= 0) {
          errors.push(`第${row}行：邀请任务price2必须大于0（目标数量）`);
        }
        break;
    }
  }

  /**
   * 解析整数值
   */
  private parseInteger(value: any, fieldName: string, defaultValue?: number): number {
    if (value === null || value === undefined || value === '') {
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      throw new Error(`${fieldName}不能为空`);
    }
    
    const parsed = parseInt(value);
    if (isNaN(parsed)) {
      throw new Error(`${fieldName}必须是数字`);
    }
    
    return parsed;
  }

  /**
   * 解析字符串值
   */
  private parseString(value: any, fieldName: string): string {
    if (value === null || value === undefined) {
      throw new Error(`${fieldName}不能为空`);
    }
    
    return String(value).trim();
  }
}
