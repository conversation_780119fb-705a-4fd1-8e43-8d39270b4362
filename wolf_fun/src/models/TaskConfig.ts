import { Model, DataTypes, Optional } from 'sequelize';
import { sequelize } from '../config/db';

// 任务类型枚举
export enum TaskType {
  UNLOCK_AREA = 1,      // 解锁指定区域
  UPGRADE_FARM = 2,     // 升级指定牧场区域至XX级
  UPGRADE_DELIVERY = 3, // 升级流水线至XX级
  INVITE_FRIENDS = 4    // 邀请好友
}

// 奖励类型
export interface RewardInfo {
  type: 'diamond' | 'box' | 'coin' | 'item';
  amount: number;
}

interface TaskConfigAttributes {
  id: number;
  condition: number;
  type: number;
  describe: string;
  price1: number;
  price2: number;
  diamond: number;
  box: number;
  coin: number;
  item: number;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

interface TaskConfigCreationAttributes extends Optional<TaskConfigAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class TaskConfig extends Model<TaskConfigAttributes, TaskConfigCreationAttributes> implements TaskConfigAttributes {
  public id!: number;
  public condition!: number;
  public type!: number;
  public describe!: string;
  public price1!: number;
  public price2!: number;
  public diamond!: number;
  public box!: number;
  public coin!: number;
  public item!: number;
  public isActive!: boolean;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  /**
   * 获取任务类型描述
   */
  public getTypeDescription(): string {
    const typeMap: { [key: number]: string } = {
      [TaskType.UNLOCK_AREA]: '解锁指定区域',
      [TaskType.UPGRADE_FARM]: '升级指定牧场区域至XX级',
      [TaskType.UPGRADE_DELIVERY]: '升级流水线至XX级',
      [TaskType.INVITE_FRIENDS]: '邀请好友'
    };
    return typeMap[this.type] || '未知任务类型';
  }

  /**
   * 获取目标进度
   */
  public getTargetProgress(): number {
    switch (this.type) {
      case TaskType.UNLOCK_AREA:
        return 1; // 解锁任务只需要完成1次
      case TaskType.UPGRADE_FARM:
        return this.price2; // 目标等级
      case TaskType.UPGRADE_DELIVERY:
        return this.price2; // 目标等级
      case TaskType.INVITE_FRIENDS:
        return this.price2; // 目标邀请数量
      default:
        return 1;
    }
  }

  /**
   * 获取奖励列表（过滤掉数量为0的奖励）
   */
  public getRewards(): RewardInfo[] {
    const rewards: RewardInfo[] = [];
    
    if (this.diamond > 0) {
      rewards.push({ type: 'diamond', amount: this.diamond });
    }
    if (this.box > 0) {
      rewards.push({ type: 'box', amount: this.box });
    }
    if (this.coin > 0) {
      rewards.push({ type: 'coin', amount: this.coin });
    }
    if (this.item > 0) {
      rewards.push({ type: 'item', amount: this.item });
    }
    
    return rewards;
  }

  /**
   * 检查是否有奖励
   */
  public hasRewards(): boolean {
    return this.diamond > 0 || this.box > 0 || this.coin > 0 || this.item > 0;
  }

  /**
   * 获取任务参数描述
   */
  public getParameterDescription(): string {
    switch (this.type) {
      case TaskType.UNLOCK_AREA:
        return `解锁区域${this.price1}`;
      case TaskType.UPGRADE_FARM:
        return `升级区域${this.price1}至${this.price2}级`;
      case TaskType.UPGRADE_DELIVERY:
        return `升级流水线至${this.price2}级`;
      case TaskType.INVITE_FRIENDS:
        return `邀请${this.price2}位好友`;
      default:
        return this.describe;
    }
  }

  /**
   * 验证任务配置的有效性
   */
  public validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查任务类型
    if (![1, 2, 3, 4].includes(this.type)) {
      errors.push('任务类型必须为1-4的数值');
    }

    // 检查任务描述
    if (!this.describe || this.describe.trim().length === 0) {
      errors.push('任务描述不能为空');
    }

    // 根据任务类型检查参数
    switch (this.type) {
      case TaskType.UNLOCK_AREA:
        if (this.price1 <= 0) {
          errors.push('解锁任务price1必须大于0（区域ID）');
        }
        break;
      case TaskType.UPGRADE_FARM:
        if (this.price1 <= 0 || this.price2 <= 0) {
          errors.push('升级任务price1和price2必须大于0');
        }
        break;
      case TaskType.UPGRADE_DELIVERY:
        if (this.price2 <= 0) {
          errors.push('流水线任务price2必须大于0（目标等级）');
        }
        break;
      case TaskType.INVITE_FRIENDS:
        if (this.price2 <= 0) {
          errors.push('邀请任务price2必须大于0（目标数量）');
        }
        break;
    }

    // 检查奖励数值
    if (this.diamond < 0 || this.box < 0 || this.coin < 0 || this.item < 0) {
      errors.push('奖励数值不能为负数');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

TaskConfig.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    condition: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '前置任务ID，0表示无前置条件',
    },
    type: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '任务类型：1-解锁区域 2-升级区域 3-升级流水线 4-邀请好友',
    },
    describe: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '任务描述',
    },
    price1: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '参数1：区域ID或其他配置',
    },
    price2: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '参数2：目标等级或数量',
    },
    diamond: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '钻石奖励',
    },
    box: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '宝箱奖励',
    },
    coin: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '金币奖励',
    },
    item: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '道具奖励',
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否激活',
    },
  },
  {
    tableName: 'task_configs',
    sequelize,
    timestamps: true,
    indexes: [
      {
        fields: ['type']
      },
      {
        fields: ['condition']
      },
      {
        fields: ['isActive']
      }
    ]
  }
);
