import { Request, Response } from "express";
import { IapPurchase, IapProduct, Booster, ActiveBooster, VipMembership, UserWallet, FarmPlot, DeliveryLine } from "../models";
import { t } from "../i18n";
import { sequelize } from "../config/db";
import { Op } from "sequelize";
import axios from "axios";
import BigNumber from 'bignumber.js';
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";


// 定义配置接口
interface VipConfig {
  durationDays: number;
  deliverySpeedBonus?: number;
  blockPriceBonus?: number;
  productionSpeedBonus?: number;
  benefits?: string[];
}

interface BundleItem {
  type: 'speed_boost' | 'time_warp';
  multiplier: number;
  duration: number;
  quantity: number;
  autoUse?: boolean;
}

interface SpecialOfferConfig {
  bundle: BundleItem[];
  accountLimit?: number;
}

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("Asia/Shanghai");

// 根据 Dapp Portal Webhook 文档实现的回调接口控制器

// Dapp Portal API 配置
const DAPP_PORTAL_BASE_URL = "https://payment.dappportal.io/api/payment-v1";
const CLIENT_ID = process.env.DAPP_PORTAL_CLIENT_ID;
const CLIENT_SECRET = process.env.DAPP_PORTAL_CLIENT_SECRET;



/**
 * 调用 Dapp Portal API 获取支付信息
 */
async function getPaymentInfo(paymentId: string) {
  try {
    const response = await axios.get(`${DAPP_PORTAL_BASE_URL}/payment/info`, {
      params: { id: paymentId },
      headers: {
        'X-Client-Id': CLIENT_ID,
        'X-Client-Secret': CLIENT_SECRET,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error: any) {
    console.error("获取支付信息失败:", error.response?.data || error.message);
    throw error;
  }
}

/**
 * 调用 Dapp Portal API 完成支付
 */
async function finalizePayment(paymentId: string) {
  try {
    const response = await axios.post(`${DAPP_PORTAL_BASE_URL}/payment/finalize`, {
      id: paymentId
    }, {
      headers: {
        'X-Client-Id': CLIENT_ID,
        'X-Client-Secret': CLIENT_SECRET,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error: any) {
    console.error("完成支付失败:", error.response?.data || error.message);
    throw error;
  }
}

/**
 * 处理 lockUrl 回调
 * 当支付流程开始前，Dapp Portal 会调用此接口锁定商品
 * Body: { paymentId: string, itemIdentifiers: string[] }
 */
export async function handleLockEvent(req: Request, res: Response) {
  try {
    const { paymentId, itemIdentifiers } = req.body;

    // 打印请求头信息
    console.log("[DappPortal][LOCK] 请求头信息:", req.headers);

    if (!paymentId || !Array.isArray(itemIdentifiers)) {
      return res.status(400).json({ ok: false, message: "参数错误" });
    }

    // 记录日志，实际项目可在此处预留库存、创建待处理记录等
    console.log("[DappPortal][LOCK] paymentId:", paymentId, "items:", itemIdentifiers);

    // 验证支付信息
    try {
      const paymentInfo = await getPaymentInfo(paymentId);
      console.log("[DappPortal][LOCK] 支付信息验证成功:", paymentInfo.status);
    } catch (error) {
      console.error("[DappPortal][LOCK] 支付信息验证失败:", error);
      // 即使验证失败也返回200，避免支付被取消
    }

    return res.status(200).json({ ok: true });
  } catch (error: any) {
    console.error("处理 lockUrl 回调失败:", error);
    return res.status(500).json({ ok: false, message: error.message || t("errors.serverError") });
  }
}

/**
 * 处理 unlockUrl 回调
 * 如果支付被系统取消，将触发此回调，需要释放锁定的商品
 * Body: { paymentId: string, itemIdentifiers: string[] }
 */
export async function handleUnlockEvent(req: Request, res: Response) {
  try {
    const { paymentId, itemIdentifiers } = req.body;

    // 打印请求头信息
    console.log("[DappPortal][UNLOCK] 请求头信息:", req.headers);

    if (!paymentId || !Array.isArray(itemIdentifiers)) {
      return res.status(400).json({ ok: false, message: "参数错误" });
    }

    console.log("[DappPortal][UNLOCK] paymentId:", paymentId, "items:", itemIdentifiers);

    // 验证支付状态
    try {
      const paymentInfo = await getPaymentInfo(paymentId);
      console.log("[DappPortal][UNLOCK] 当前支付状态:", paymentInfo.status);
    } catch (error) {
      console.error("[DappPortal][UNLOCK] 获取支付状态失败:", error);
    }

    // 如果存在对应的 IapPurchase 记录，更新其状态为取消
    try {
      const purchase = await IapPurchase.findOne({ where: { paymentId } });
      if (purchase) {
        await purchase.update({ 
          status: "CANCELED",
          statusChecked: true // 标记为已通过回调处理
        });
        console.log("[DappPortal][UNLOCK] 已更新 IapPurchase 状态为 CANCELED");
      }
    } catch (dbErr) {
      console.error("更新 IapPurchase 状态失败:", dbErr);
    }

    return res.status(200).json({ ok: true });
  } catch (error: any) {
    console.error("处理 unlockUrl 回调失败:", error);
    return res.status(500).json({ ok: false, message: error.message || t("errors.serverError") });
  }
}

/**
 * 处理 paymentStatusChangeCallbackUrl 回调
 * 支付状态变更都会触发此回调
 * Body: { paymentId: string, status: string }
 */
export async function handleStatusChangeEvent(req: Request, res: Response) {
  try {
    let { paymentId, status } = req.body;

    // 打印请求头信息
    console.log("[DappPortal][STATUS] 请求头信息:", req.headers);

    if (!paymentId || !status) {
      return res.status(400).json({ ok: false, message: "参数错误" });
    }

    console.log("[DappPortal][STATUS] paymentId:", paymentId, "status:", status);

    // 核查支付状态
    let verifiedPaymentInfo = null;
    try {
      verifiedPaymentInfo = await getPaymentInfo(paymentId);
      console.log("[DappPortal][STATUS] 核查支付状态:", verifiedPaymentInfo.status);
      
      // 检查回调状态与实际状态是否一致
      if (verifiedPaymentInfo.status !== status) {
        console.warn("[DappPortal][STATUS] 状态不一致! 回调状态:", status, "实际状态:", verifiedPaymentInfo.status);
        // 使用实际查询到的状态
        status = verifiedPaymentInfo.status;
      }
    } catch (error) {
      console.error("[DappPortal][STATUS] 核查支付状态失败:", error);
      // 继续使用回调中的状态
    }

    // 直接使用 Dapp Portal 的状态
    const validStatuses = ['PENDING', 'CONFIRMED', 'FINALIZED', 'REFUNDED', 'CONFIRM_FAILED', 'CANCELED', 'CHARGEBACK'];
    const finalStatus = validStatuses.includes(status) ? status : 'PENDING';

    // 更新对应的 IapPurchase 记录
    try {
      const purchase = await IapPurchase.findOne({ 
        where: { paymentId },
        include: [{ model: IapProduct }]
      });
      if (purchase) {
        // 避免重复处理
        if (purchase.status === finalStatus) {
          console.log(`[DappPortal][STATUS] Purchase ${paymentId} already processed with status: ${finalStatus}`);
          return res.status(200).json({ ok: true });
        }

        await purchase.update({ 
          status: finalStatus as any,
          statusChecked: true // 标记为已通过回调处理
        });
        console.log("[DappPortal][STATUS] 已更新 IapPurchase 状态为:", finalStatus);
        
        // 只有在状态为 FINALIZED 时才发放商品
        if (finalStatus === "FINALIZED") {
          await fulfillPurchase(purchase);
          console.log(`[DappPortal][STATUS] Purchase ${paymentId} fulfilled successfully`);
        }
      }
    } catch (dbErr) {
      console.error("更新 IapPurchase 状态失败:", dbErr);
    }

    // 如果状态为 CONFIRMED，调用 finalize API
    if (status === "CONFIRMED") {
      try {
        console.log("[DappPortal][STATUS] 支付已确认，正在调用 finalize API...");
        const finalizeResult = await finalizePayment(paymentId);
        console.log("[DappPortal][STATUS] finalize API 调用成功:", finalizeResult);
      } catch (finalizeError) {
        console.error("[DappPortal][STATUS] finalize API 调用失败:", finalizeError);
        // finalize 失败不影响回调响应
      }
    }

    return res.status(200).json({ ok: true });
  } catch (error: any) {
    console.error("处理 paymentStatusChange 回调失败:", error);
    return res.status(500).json({ ok: false, message: error.message || t("errors.serverError") });
  }
}

/**
 * 发放购买的商品
 */
export async function fulfillPurchase(purchase: any) {
  const transaction = await sequelize.transaction();

  try {
    const product = purchase.IapProduct;
    const walletId = purchase.walletId;

    switch (product.type) {
      case 'speed_boost':
      case 'time_warp':
        // 每次购买都创建新的道具记录，不累加到现有记录
        await Booster.create({
          walletId,
          type: product.type,
          multiplier: product.multiplier,
          duration: product.duration,
          quantity: product.quantity || 1
        }, { transaction });

        // 时间跳跃道具不再自动使用，改为手动使用
        break;

      case 'vip_membership':
        // 检查VIP会员购买限制
        const now = dayjs();
        
        // 检查是否已有活跃的VIP会员
        const existingVip = await VipMembership.findOne({
          where: { walletId, isActive: true },
          transaction
        });
        
        // 如果用户已经是VIP会员且未过期，则不允许重复购买
        if (existingVip && dayjs(existingVip.endTime).isAfter(now)) {
          throw new Error('您已经是VIP会员，无法重复购买VIP会员资格');
        }
        
        // 解析VIP配置
        let config: VipConfig = { durationDays: 30 };
        try {
          config = typeof product.config === 'string' ? JSON.parse(product.config) : (product.config || { durationDays: 30 });
        } catch (e) {
          console.error('解析VIP配置失败:', e);
          config = { durationDays: 30 };
        }
        const durationDays = config.durationDays || 30;
        
        // 创建新VIP会员
        const startTime = now.toDate();
        const endTime = now.add(durationDays, 'day').toDate();

        await VipMembership.upsert({
          walletId,
          startTime,
          endTime,
          isActive: true
        }, { transaction });
        break;

      case 'special_offer':
        // 处理特殊套餐
        let bundleConfig: SpecialOfferConfig = { bundle: [] };
        try {
          bundleConfig = typeof product.config === 'string' ? JSON.parse(product.config) : (product.config || { bundle: [] });
        } catch (e) {
          console.error('解析Special Offer配置失败:', e);
          bundleConfig = { bundle: [] };
        }
        
        // 检查购买限制
        const accountLimit = bundleConfig.accountLimit;
        if (accountLimit) {
          const existingPurchases = await IapPurchase.count({
            where: {
              walletId,
              productId: product.id,
              status: 'completed'
            },
            transaction
          });
          
          if (existingPurchases >= accountLimit) {
            throw new Error(`该商品每个账户限购${accountLimit}次，您已达到购买上限`);
          }
        }
        
        const bundle = bundleConfig.bundle || [];
        
        for (const item of bundle) {
          // 所有道具都添加到背包，不再自动使用时间跳跃
          // 每次购买都创建新的道具记录，不累加到现有记录
          await Booster.create({
            walletId,
            type: item.type,
            multiplier: item.multiplier,
            duration: item.duration,
            quantity: item.quantity
          }, { transaction });
        }
        break;
    }

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

// 旧的useTimeWarp函数已被TimeWarpService.executeTimeWarp替代