# 离线奖励API文档

## 概述

离线奖励系统允许玩家在不活跃期间继续获得基于出货线的收益。如果用户在2分钟内没有活跃（通过调用 `/api/wallet/increase-gem` 接口更新 `lastActiveTime`），则视为离线状态。离线奖励最多累积8小时。

## 累积奖励功能

**重要更新**: 离线奖励现在支持累积功能！

- **持续累积**: 离线奖励会自动累积到用户账户中，不会因为未及时领取而丢失
- **灵活领取**: 用户可以查看累积奖励而不立即领取，选择合适的时机一次性领取
- **数据持久化**: 累积奖励保存在数据库中，确保数据安全
- **增量计算**: 系统只计算新增的奖励并累加，提高性能

## 离线状态判断

- **在线状态**: 2分钟内有活跃记录（`lastActiveTime` 在2分钟内）
- **离线状态**: 超过2分钟没有活跃记录，或者从未有过活跃记录
- **最大离线奖励时间**: 8小时
- **特殊情况**: 如果用户从未活跃过（`lastActiveTime` 为空），则不返回离线奖励

## 离线奖励计算规则

离线奖励采用与时间跳跃相同的完整收益链计算方式：

1. **时间内生产牛奶** = 所有牧场区每秒生产牛奶 × 离线时间（秒）
2. **时间内可处理牛奶** = 出货线每秒处理牛奶 × 离线时间（秒）
3. **实际处理牛奶** = min(时间内生产牛奶, 时间内可处理牛奶)
4. **获得GEM** = 实际处理牛奶 ÷ 方块单位 × 方块价格

### 计算说明
- 模拟完整的生产→出货→获得GEM流程
- 自动应用VIP加成（+30%出货速度，+20%方块价格，+30%农场生产速度）
- 自动应用速度加成道具的影响
- 受农场产量和出货线处理能力中较小值限制

## 1. 获取离线奖励信息

### 请求

- **URL**: `/api/wallet/offline-reward`
- **方法**: `GET`
- **认证**: 需要钱包认证 (walletAuthMiddleware)

### 响应

#### 成功响应 (200 OK)

**在线状态响应**:
```json
{
  "success": true,
  "data": {
    "isOffline": false,
    "offlineTime": 0,
    "offlineReward": {
      "gem": 0
    }
  },
  "message": "Offline reward retrieved successfully"
}
```

**离线状态响应（累积奖励模式）**:
```json
{
  "success": true,
  "data": {
    "isOffline": true,
    "offlineTime": 7200,
    "offlineReward": {
      "gem": 150.500,
      "milkProduced": 18000.000,
      "milkProcessed": 14400.000,
      "farmProductionPerSecond": 2.500,
      "deliveryProcessingPerSecond": 2.000,
      "hasVip": false,
      "hasSpeedBoost": false,
      "speedBoostMultiplier": 1.00
    },
    "lastActiveTime": "2025-07-23T08:00:00.000Z"
  },
  "message": "Offline reward retrieved successfully"
}
```

**从未活跃用户响应**:
```json
{
  "success": true,
  "data": {
    "isOffline": false,
    "offlineTime": 0,
    "offlineReward": {
      "gem": 0
    }
  },
  "message": "Offline reward retrieved successfully"
}
```

#### 字段说明（累积奖励模式）

- `isOffline`: 是否处于离线状态
- `offlineTime`: 当前离线时间（秒）
- `offlineReward.gem`: 累积的离线宝石奖励数量
- `offlineReward.milkProduced`: 离线期间生产的牛奶数量（兼容性字段）
- `offlineReward.milkProcessed`: 离线期间处理的牛奶数量（兼容性字段）
- `offlineReward.farmProductionPerSecond`: 当前农场每秒产量
- `offlineReward.deliveryProcessingPerSecond`: 当前出货线每秒处理量
- `offlineReward.hasVip`: 是否有VIP加成
- `offlineReward.hasSpeedBoost`: 是否有速度加成
- `offlineReward.speedBoostMultiplier`: 速度加成倍数
- `lastActiveTime`: 用户最后活跃时间

#### 错误响应

**404 - 用户钱包不存在**:
```json
{
  "success": false,
  "message": "Wallet does not exist",
  "error": "User wallet not found"
}
```

**404 - 出货线不存在**:
```json
{
  "success": false,
  "message": "Delivery line does not exist", 
  "error": "Delivery line not found"
}
```

**500 - 服务器内部错误**:
```json
{
  "success": false,
  "message": "Failed to get offline reward",
  "error": "具体错误信息"
}
```

## 2. 结算离线奖励

### 请求

- **URL**: `/api/wallet/claim-offline-reward`
- **方法**: `POST`
- **认证**: 需要钱包认证 (walletAuthMiddleware)

### 响应

#### 成功响应 (200 OK) - 累积奖励模式

```json
{
  "success": true,
  "data": {
    "claimedReward": {
      "gem": 150.500
    },
    "remainingReward": {
      "gem": 0.000
    },
    "currentGem": 1250.750
  },
  "message": "Offline reward claimed successfully"
}
```

#### 字段说明

- `claimedReward.gem`: 本次领取的累积宝石数量
- `remainingReward.gem`: 领取后剩余的累积奖励（通常为0）
- `currentGem`: 领取后的宝石总余额

#### 错误响应

**400 - 用户未处于离线状态**:
```json
{
  "success": false,
  "message": "Not offline",
  "error": "User is still online"
}
```

**400 - 用户从未活跃过**:
```json
{
  "success": false,
  "message": "Not offline",
  "error": "User has never been active"
}
```

**404 - 用户钱包不存在**:
```json
{
  "success": false,
  "message": "Wallet does not exist",
  "error": "User wallet not found"
}
```

**404 - 出货线不存在**:
```json
{
  "success": false,
  "message": "Delivery line does not exist",
  "error": "Delivery line not found"
}
```

**500 - 服务器内部错误**:
```json
{
  "success": false,
  "message": "Failed to claim offline reward",
  "error": "具体错误信息"
}
```

## 钱包历史记录

离线奖励结算时会自动创建钱包历史记录：

### 宝石奖励记录
- `currency`: "GEM"
- `action`: "IN"
- `category`: "OFFLINE_REWARD"
- `credit_type`: "OFFLINE_REWARD"
- `reference`: "离线奖励" (根据语言设置)
- `fe_display_remark`: "离线奖励获得 {amount} 宝石" (根据语言设置)
- `developer_remark`: "离线奖励获得宝石: {amount}"

## 使用流程

1. **检查离线奖励**: 调用 `GET /api/wallet/offline-reward` 检查是否有离线奖励
2. **结算奖励**: 如果有奖励，调用 `POST /api/wallet/claim-offline-reward` 进行结算
3. **更新活跃时间**: 每次调用 `/api/wallet/increase-gem` 时会自动更新最后活跃时间

## 注意事项

1. 离线奖励最多累积8小时，超过8小时的部分不会计算
2. 只有在离线状态（超过1分钟未活跃）时才能获得离线奖励
3. 如果用户从未活跃过（`lastActiveTime` 为空），则不会获得离线奖励
4. 结算离线奖励后会重置最后活跃时间为当前时间
5. 离线奖励基于当前农场区块和出货线配置计算，升级可以提高离线收益
6. 离线奖励采用完整的生产→出货→获得GEM流程计算
7. 自动应用VIP和速度加成道具的影响
8. 受农场产量和出货线处理能力中较小值限制
9. 所有错误和成功消息都支持多语言（中文/英文）