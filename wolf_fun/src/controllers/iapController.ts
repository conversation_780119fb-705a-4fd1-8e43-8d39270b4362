import { Request, Response } from 'express';
import { UserWallet } from '../models/UserWallet';
import { IapProduct } from '../models/IapProduct';
import { IapPurchase } from '../models/IapPurchase';
import { Booster } from '../models/Booster';
import { ActiveBooster } from '../models/ActiveBooster';
import { VipMembership } from '../models/VipMembership';
import { FarmPlot } from '../models/FarmPlot';
import { DeliveryLine } from '../models/DeliveryLine';
import { Op } from 'sequelize';
import { sequelize } from '../config/db';
import { MyRequest } from '../types/customRequest';
import { v4 as uuidv4 } from 'uuid';
import { handleLockEvent, handleUnlockEvent, handleStatusChangeEvent } from './dappPortalPaymentController';
import BigNumber from 'bignumber.js';
import { tFromRequest, t } from '../i18n';
import { errorResponse } from '../utils/responseUtil';
import dayjs from 'dayjs';
import { TimeWarpService } from '../services/timeWarpService';
import { BoosterMutexService } from '../services/boosterMutexService';

class IapController {
  // 获取商店商品列表
  async getStoreProducts(req: MyRequest, res: Response) {
    try {
      const walletId = Number(req.user?.walletId);

      if (!walletId) {
        return res.status(400).json({ ok: false, message: tFromRequest(req, 'errors.iap.walletIdRequired') });
      }

      // 获取所有活跃的商品
      const products = await IapProduct.findAll({
        where: { isActive: true },
        order: [['type', 'ASC'], ['priceUsd', 'ASC']]
      });

      // 检查每个商品的购买限制
      const today = dayjs().startOf('day').toDate();
      const tomorrow = dayjs().startOf('day').add(1, 'day').toDate();

      const productsWithAvailability = await Promise.all(
        products.map(async (product) => {
          let canPurchase = true;
          let reason = '';
        let timeWarpRewards = null;

        // 如果是时间跳跃商品，计算预期收益
        if (product.type === 'time_warp' && product.duration) {
          try {
            timeWarpRewards = await TimeWarpService.previewTimeWarpRewards(walletId, product.duration);
          } catch (error) {
            console.error('计算时间跳跃收益失败:', error);
          }
        }

          // 检查每日限制 - 按商品类型限制
          if (product.type === 'speed_boost' || product.type === 'time_warp') {
            // 查询今日同类型商品的购买次数
            const todayTypeProducts = await IapProduct.findAll({
              where: {
                type: product.type,
                isActive: true
              },
              attributes: ['id']
            });

            const productIds = todayTypeProducts.map(p => p.id);
            
            const todayTypePurchases = await IapPurchase.count({
              where: {
                walletId,
                productId: {
                  [Op.in]: productIds
                },
                status: 'completed',
                purchaseDate: {
                  [Op.gte]: today,
                  [Op.lt]: tomorrow
                }
              }
            });

            if (todayTypePurchases >= 1) {
              canPurchase = false;
              reason = tFromRequest(req, 'errors.iap.dailyTypeLimitReached', {
                productType: product.type.replace('_', ' ')
              });
            }
          } else if (product.dailyLimit > 0) {
            // 其他商品仍使用单个商品的每日限制
            const todayPurchases = await IapPurchase.count({
              where: {
                walletId,
                productId: product.id,
                status: 'completed',
                purchaseDate: {
                  [Op.gte]: today,
                  [Op.lt]: tomorrow
                }
              }
            });

            if (todayPurchases >= product.dailyLimit) {
              canPurchase = false;
              reason = tFromRequest(req, 'errors.iap.dailyLimitReached', {
                productName: product.name
              });
            }
          }

          // 检查账号限制
          if (product.accountLimit && product.accountLimit > 0) {
            const totalPurchases = await IapPurchase.count({
              where: {
                walletId,
                productId: product.id,
                status: 'completed'
              }
            });

            if (totalPurchases >= product.accountLimit) {
              canPurchase = false;
              reason = tFromRequest(req, 'errors.iap.accountLimitReached', {
                productName: product.name
              });
            }
          }

          // 检查VIP会员是否已存在
          if (product.type === 'vip_membership') {
            const existingVip = await VipMembership.findOne({
              where: {
                walletId,
                isActive: true,
                endTime: { [Op.gt]: dayjs().toDate() }
              }
            });

            if (existingVip) {
              canPurchase = false;
              reason = tFromRequest(req, 'errors.iap.vipAlreadyActive');
            }
          }

          const productData = product.toJSON();
          
          // 添加多语言描述
          // 使用产品类型的通用翻译
          const productTypeKey = `iap.products.${product.type}.description`;
          
          // 从请求上下文获取翻译
          const translatedByType = tFromRequest(req, productTypeKey);
          
          // 确定最终描述：优先使用类型翻译，其次使用原始描述
          const finalDescription = 
            translatedByType !== productTypeKey ? translatedByType :
            productData.description;
          
          const result: any = {
            ...productData,
            description: finalDescription,
            canPurchase,
            reason
          };

          // 如果是时间跳跃商品，添加预期收益信息
          if (timeWarpRewards) {
            result.expectedRewards = {
              gemsEarned: timeWarpRewards.gemsEarned,
              milkProduced: timeWarpRewards.milkProduced,
              milkProcessed: timeWarpRewards.milkProcessed,
              farmProductionPerSecond: timeWarpRewards.farmProductionPerSecond,
              deliveryProcessingPerSecond: timeWarpRewards.deliveryProcessingPerSecond,
              hasVip: timeWarpRewards.hasVip,
              hasSpeedBoost: timeWarpRewards.hasSpeedBoost,
              speedBoostMultiplier: timeWarpRewards.speedBoostMultiplier
            };
          }

          return result;
        })
      );

      res.json({
        ok: true,
        products: productsWithAvailability
      });
    } catch (error) {
      console.error('Get store products error:', error);
      res.status(500).json({ ok: false, message: tFromRequest(req, 'errors.serverError') });
    }
  }

  // 创建支付订单
  async createPayment(req: MyRequest, res: Response) {
    try {
      const { productId, paymentMethod, testMode, imageUrl } = req.body;

      const walletId = Number(req.user?.walletId);

      if (!walletId || !productId || !paymentMethod) {
        return res.status(400).json({ ok: false, message: tFromRequest(req, 'errors.iap.missingRequiredParameters') });
      }

      // 获取单个商品信息
      const product = await IapProduct.findOne({
        where: {
          id: productId,
          isActive: true
        }
      });

      if (!product) {
        return res.status(404).json({ ok: false, message: tFromRequest(req, 'errors.iap.productNotFound') });
      }

      // 验证用户钱包
      const wallet = await UserWallet.findByPk(walletId);
      if (!wallet) {
        return res.status(404).json({ ok: false, message: tFromRequest(req, 'errors.iap.walletNotFound') });
      }

      // 检查购买限制
      const today = dayjs().startOf('day').toDate();
      const tomorrow = dayjs().startOf('day').add(1, 'day').toDate();

      // 检查购买限制
      // 检查每日限制 - 按商品类型限制
      if (product.type === 'speed_boost' || product.type === 'time_warp') {
        // 查询今日同类型商品的购买次数
        const todayTypeProducts = await IapProduct.findAll({
          where: {
            type: product.type,
            isActive: true
          },
          attributes: ['id']
        });

        const productIds = todayTypeProducts.map(p => p.id);
        
        const todayTypePurchases = await IapPurchase.count({
          where: {
            walletId,
            productId: {
              [Op.in]: productIds
            },
            status: 'completed',
            purchaseDate: {
              [Op.gte]: today,
              [Op.lt]: tomorrow
            }
          }
        });

        if (todayTypePurchases >= 1) {
          const errorMessage = tFromRequest(req, 'errors.iap.dailyTypeLimitReached', {
            productType: product.type
          });
          return res.status(400).json({
            ok: false,
            message: errorMessage,
            productType: product.type,
            limit: 1,
            purchased: todayTypePurchases
          });
        }
      } else if (product.dailyLimit > 0) {
        // 其他商品仍使用单个商品的每日限制
        const todayPurchases = await IapPurchase.count({
          where: {
            walletId,
            productId: product.id,
            status: 'completed',
            purchaseDate: {
              [Op.gte]: today,
              [Op.lt]: tomorrow
            }
          }
        });

        if (todayPurchases >= product.dailyLimit) {
          const errorMessage = tFromRequest(req, 'errors.iap.dailyLimitReached', {
            productName: product.name
          });
          return res.status(400).json({
            ok: false,
            message: errorMessage,
            productId: product.id,
            limit: product.dailyLimit,
            purchased: todayPurchases
          });
        }
      }

      // 检查账号限制
      if (product.accountLimit && product.accountLimit > 0) {
        const totalPurchases = await IapPurchase.count({
          where: {
            walletId,
            productId: product.id,
            status: 'completed'
          }
        });

        if (totalPurchases >= product.accountLimit) {
          const errorMessage = tFromRequest(req, 'errors.iap.accountLimitReached', {
            productName: product.name
          });
          return res.status(400).json({
            ok: false,
            message: errorMessage,
            productId: product.id,
            limit: product.accountLimit,
            purchased: totalPurchases
          });
        }
      }

      // 检查VIP会员是否已存在
      if (product.type === 'vip_membership') {
        const existingVip = await VipMembership.findOne({
          where: {
            walletId,
            isActive: true,
            endTime: { [Op.gt]: dayjs().toDate() }
          }
        });

        if (existingVip) {
          const errorMessage = tFromRequest(req, 'errors.iap.vipAlreadyActive');
          return res.status(400).json({
            ok: false,
            message: errorMessage,
            endTime: existingVip.endTime
          });
        }
      }

      // 计算商品金额
      const currency = paymentMethod === 'kaia' ? 'KAIA' : 'USD';
      const amount = paymentMethod === 'kaia' ? product.priceKaia : product.priceUsd;
      
      if (!amount) {
        const errorMessage = tFromRequest(req, 'errors.iap.priceNotAvailable', {
          productName: product.name
        });
        return res.status(400).json({
          ok: false,
          message: errorMessage,
          productId: product.id
        });
      }
      
      // 使用 BigNumber 处理价格计算，保留4位小数
      const productAmountBN = new BigNumber(amount).decimalPlaces(4, BigNumber.ROUND_HALF_UP);
      const productAmount = productAmountBN.toNumber();
      console.log('产品价格:', productAmount, currency);

      // 调用 DappPortal 创建支付订单
      const DAPP_PORTAL_BASE_URL = "https://payment.dappportal.io/api/payment-v1";
      const CLIENT_ID = process.env.DAPP_PORTAL_CLIENT_ID;
      const CLIENT_SECRET = process.env.DAPP_PORTAL_CLIENT_SECRET;
      const BASE_URL = process.env.BASE_URL || 'http://localhost:3456';

      console.log('BASE_URL',BASE_URL);
      
      
      if (!CLIENT_ID || !CLIENT_SECRET) {
        return res.status(500).json({ ok: false, message: tFromRequest(req, 'errors.iap.dappPortalConfigMissing') });
      }

      // 获取用户的DappPortal地址（钱包地址）
      const buyerDappPortalAddress = wallet.walletAddress;
      if (!buyerDappPortalAddress) {
        return res.status(400).json({ ok: false, message: tFromRequest(req, 'errors.iap.userWalletAddressNotFound') });
      }

      // 根据DappPortal文档创建支付订单请求
      // 对于STRIPE支付，price字段需要使用最小单位（美分），即乘以100
      // 对于USD、THB、TWD等货币，需要乘以100
      const mainPriceBN = paymentMethod === 'kaia' 
        ? productAmountBN 
        : productAmountBN.multipliedBy(100);
      const mainPrice = mainPriceBN.toString();
      
      // items中的price字段也需要与主price保持一致
      const itemPrice = mainPrice;
      
      const item = {
        itemIdentifier: `${product.type}_${product.id}`,
        name: product.name.trim(),
        imageUrl: imageUrl || '',
        price: itemPrice, // items中的price需要与主price保持一致
        currencyCode: currency
      };
      
      console.log('构建的商品对象:', JSON.stringify(item, null, 2));

      const paymentRequest = {
        buyerDappPortalAddress: buyerDappPortalAddress.trim(),
        pgType: paymentMethod === 'kaia' ? 'CRYPTO' : 'STRIPE',
        currencyCode: currency,
        price: mainPrice, // 主price字段：STRIPE使用最小单位（乘以100），CRYPTO使用原始价格
        paymentStatusChangeCallbackUrl: `${BASE_URL}/api/dapp-portal-payment/status`.trim(),
        lockUrl: `${BASE_URL}/api/dapp-portal-payment/lock`.trim(),
        unlockUrl: `${BASE_URL}/api/dapp-portal-payment/unlock`.trim(),
        items: [item],
        testMode: testMode !== undefined ? testMode : (process.env.NODE_ENV !== 'production')
      };

      let dappPortalResponse;

      console.log('=== 支付请求对象 ===');
      console.log(JSON.stringify(paymentRequest, null, 2));

      try {
        const axios = require('axios');
        dappPortalResponse = await axios.post(
          `${DAPP_PORTAL_BASE_URL}/payment/create`,
          paymentRequest,
          {
            headers: {
              'X-Client-Id': CLIENT_ID,
              'X-Client-Secret': CLIENT_SECRET,
              'Content-Type': 'application/json'
            }
          }
        );
        
        console.log('DappPortal API 调用成功:', dappPortalResponse.status);
        
      } catch (dappError: any) {
        console.error('DappPortal API 调用失败:', dappError.response?.data);
        
        return res.status(500).json({
          ok: false,
          message: tFromRequest(req, 'errors.iap.failedToCreatePaymentOrder'),
          details: dappError.response?.data?.message || tFromRequest(req, 'errors.iap.dappPortalUnavailable')
        });
      }

      const paymentData = dappPortalResponse.data;
      const paymentId = paymentData.id;

      if (!paymentId) {
        return res.status(500).json({ ok: false, message: tFromRequest(req, 'errors.iap.invalidPaymentResponse') });
      }

      // 创建购买记录
      const purchase = await IapPurchase.create({
        walletId,
        productId: product.id,
        paymentId,
        paymentMethod: paymentMethod === 'kaia' ? 'kaia' : 'stripe', // 确保 paymentMethod 只能是 'kaia' 或 'stripe'
        amount: productAmount,
        currency,
        status: 'CREATED',
        statusChecked:false,
        purchaseDate: dayjs().toDate()
      });

      // 返回支付信息
      res.json({
        ok: true,
        paymentId,
        amount: productAmount,
        purchase: purchase.toJSON(),
        product: { id: product.id, name: product.name, type: product.type }
      });
    } catch (error) {
      console.error('Create payment error:', error);
      res.status(500).json({ ok: false, message: tFromRequest(req, 'errors.serverError') });
    }
  }

  // 获取用户拥有的道具
  async getUserBoosters(req: MyRequest, res: Response) {
    try {
      const walletId = Number(req.user?.walletId);

      console.log('walletId',walletId);
      

      if (!walletId) {
        return res.status(400).json({ ok: false, message: tFromRequest(req, 'errors.iap.walletIdRequired') });
      }

      // 查询用户拥有的道具
      const boosters = await Booster.findAll({
        where: {
          walletId,
          quantity: { [Op.gt]: 0 }
        }
      });

      // 为每个booster查找对应的IapProduct
      const boostersWithProducts = await Promise.all(boosters.map(async (booster) => {
        const product = await IapProduct.findOne({
          where: {
            type: booster.type,
            multiplier: booster.multiplier,
            duration: booster.duration,
            isActive: true
          },
          attributes: ['id', 'productId', 'name', 'type', 'multiplier', 'duration', 'description']
        });
        
        return {
          ...booster.toJSON(),
          product: product ? product.toJSON() : null
        };
      }));

     
      
      res.json({
        ok: true,
        boosters: boostersWithProducts,
      });
    } catch (error) {
      console.error('Get user boosters error:', error);
      res.status(500).json({ ok: false, message: tFromRequest(req, 'errors.serverError') });
    }
  }

  // 使用道具
  async useBooster(req: MyRequest, res: Response) {
    try {
      const walletId = Number(req.user?.walletId);
      const { boosterId } = req.body;

      if (!walletId || !boosterId) {
        return res.status(400).json({ ok: false, message: tFromRequest(req, 'errors.iap.walletIdAndBoosterIdRequired') });
      }

      const transaction = await sequelize.transaction();

      try {
        // 查找道具
        const booster = await Booster.findOne({
          where: {
            id: boosterId,
            walletId,
            quantity: { [Op.gt]: 0 }
          },
          transaction
        });

        if (!booster) {
          await transaction.rollback();
          return res.status(404).json({ ok: false, message: tFromRequest(req, 'errors.iap.boosterNotFoundOrInsufficient') });
        }

        // 时间跳跃道具特殊处理：不需要互斥检查，直接执行
        if (booster.type === 'time_warp') {
          console.log(`🚀 开始执行时间跳跃: walletId=${walletId}, duration=${booster.duration}小时`);

          // 获取使用前的GEM余额
          const walletBefore = await UserWallet.findOne({ where: { id: walletId }, transaction });
          const gemBefore = Number(walletBefore?.gem || 0);
          console.log(`💰 使用前GEM余额: ${gemBefore}`);

          // 执行时间跳跃
          const rewards = await TimeWarpService.executeTimeWarp(
            walletId,
            booster.duration,
            transaction
          );

          console.log(`🎁 时间跳跃奖励计算结果:`, rewards);

          // 获取使用后的GEM余额
          const walletAfter = await UserWallet.findOne({ where: { id: walletId }, transaction });
          const gemAfter = Number(walletAfter?.gem || 0);
          console.log(`💰 使用后GEM余额: ${gemAfter}`);
          console.log(`📈 GEM增加量: ${gemAfter - gemBefore}`);

          // 减少道具数量
          booster.quantity -= 1;
          await booster.save({ transaction });

          // 如果数量为0，删除记录
          if (booster.quantity <= 0) {
            await booster.destroy({ transaction });
          }

          await transaction.commit();

          return res.json({
            ok: true,
            message: tFromRequest(req, 'success.iap.timeWarpExecuted'),
            rewards,
            remainingQuantity: Math.max(0, booster.quantity),
            debug: {
              gemBefore,
              gemAfter,
              gemIncrease: gemAfter - gemBefore
            }
          });
        }

        // 其他道具使用互斥检查服务
        const mutexCheck = await BoosterMutexService.canUseBooster(walletId, booster.type);

        if (!mutexCheck.canUse) {
          await transaction.rollback();

          // 根据不同的冲突原因返回不同的错误消息
          let errorMessage: string;
          if (mutexCheck.reason === 'SPEED_BOOST_ALREADY_ACTIVE') {
            const remainingTime = mutexCheck.conflictingBooster
              ? BoosterMutexService.formatRemainingTime(mutexCheck.conflictingBooster.endTime)
              : '';
            errorMessage = tFromRequest(req, 'errors.iap.speedBoostAlreadyActive', { remainingTime });
          } else {
            errorMessage = tFromRequest(req, 'errors.iap.boosterMutexConflict');
          }

          return res.status(400).json({
            ok: false,
            message: errorMessage,
            conflictingBooster: mutexCheck.conflictingBooster ? {
              id: mutexCheck.conflictingBooster.id,
              type: mutexCheck.conflictingBooster.type,
              multiplier: mutexCheck.conflictingBooster.multiplier,
              endTime: mutexCheck.conflictingBooster.endTime,
              remainingTime: BoosterMutexService.formatRemainingTime(mutexCheck.conflictingBooster.endTime)
            } : undefined
          });
        }

        // 减少道具数量
        booster.quantity -= 1;
        await booster.save({ transaction });

        // 创建激活道具记录
        const now = dayjs().toDate();
        const endTime = dayjs().add(booster.duration, 'hour').toDate();
        // 根据道具类型和属性查找对应的IapProduct
        const iapProduct = await IapProduct.findOne({
          where: {
            type: booster.type,
            multiplier: booster.multiplier,
            duration: booster.duration,
            isActive: true
          },
          transaction
        });

        const activeBooster = await ActiveBooster.create({
          walletId,
          productId: iapProduct?.id || undefined,
          type: booster.type,
          multiplier: booster.multiplier,
          startTime: now,
          endTime,
          status: 'active'
        }, { transaction });

        await transaction.commit();

        res.json({
          ok: true,
          activeBooster: activeBooster.toJSON(),
          message: tFromRequest(req, 'success.iap.boosterActivated', { boosterType: booster.type })
        });
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    } catch (error) {
      console.error('Use booster error:', error);
      res.status(500).json({ ok: false, message: tFromRequest(req, 'errors.serverError') });
    }
  }

  // 预览时间跳跃收益
  async previewTimeWarpRewards(req: MyRequest, res: Response) {
    try {
      const walletId = Number(req.user?.walletId);
      const { hours } = req.query;

      if (!walletId) {
        return res.status(400).json({ ok: false, message: tFromRequest(req, 'errors.iap.walletIdRequired') });
      }

      if (!hours || isNaN(Number(hours))) {
        return res.status(400).json({ ok: false, message: '请提供有效的小时数' });
      }

      const hoursNum = Number(hours);
      if (hoursNum <= 0 || hoursNum > 24) {
        return res.status(400).json({ ok: false, message: '小时数必须在1-24之间' });
      }

      const rewards = await TimeWarpService.previewTimeWarpRewards(walletId, hoursNum);

      res.json({
        ok: true,
        rewards
      });
    } catch (error) {
      console.error('Preview time warp rewards error:', error);
      res.status(500).json({ ok: false, message: tFromRequest(req, 'errors.serverError') });
    }
  }



  // 获取VIP状态
  async getVipStatus(req: MyRequest, res: Response) {
    try {
      const walletId = Number(req.user?.walletId);

      if (!walletId) {
        return res.status(400).json({ ok: false, message: tFromRequest(req, 'errors.iap.walletIdRequired') });
      }

      const vipMembership = await VipMembership.findOne({
        where: { walletId }
      });

      if (!vipMembership) {
        return res.json({
          ok: true,
          isVip: false,
          membership: null
        });
      }

      // 检查并更新VIP状态
      const isActive = vipMembership.checkAndUpdateStatus();

      res.json({
        ok: true,
        isVip: isActive,
        membership: vipMembership.toJSON()
      });
    } catch (error) {
      console.error('Get VIP status error:', error);
      res.status(500).json({ ok: false, message: tFromRequest(req, 'errors.serverError') });
    }
  }

  // 获取用户的激活道具
  async getActiveBoosters(req: MyRequest, res: Response) {
    try {
      const walletId = Number(req.user?.walletId);

      if (!walletId) {
        return res.status(400).json({ ok: false, message: tFromRequest(req, 'errors.iap.walletIdRequired') });
      }

      const activeBoosters = await ActiveBooster.findAll({
        where: {
          walletId,
          endTime: { [Op.gt]: dayjs().toDate() }
        },
        include: [{
          model: IapProduct,
          as: 'product',
          attributes: ['id', 'name', 'description', 'type', 'priceUsd', 'priceKaia', 'config']
        }],
        order: [['endTime', 'ASC']]
      });

      res.json({
        ok: true,
        activeBoosters
      });
    } catch (error) {
      console.error('Get active boosters error:', error);
      res.status(500).json({ ok: false, message: tFromRequest(req, 'errors.serverError') });
    }
  }

  // 获取道具互斥状态
  async getBoosterMutexStatus(req: MyRequest, res: Response) {
    try {
      const walletId = Number(req.user?.walletId);

      if (!walletId) {
        return res.status(400).json({ ok: false, message: tFromRequest(req, 'errors.iap.walletIdRequired') });
      }

      const mutexStatus = await BoosterMutexService.getBoosterMutexStatus(walletId);

      res.json({
        ok: true,
        mutexStatus: {
          hasActiveSpeedBoost: mutexStatus.hasActiveSpeedBoost,
          hasActiveTimeWarp: mutexStatus.hasActiveTimeWarp,
          canUseSpeedBoost: mutexStatus.canUseSpeedBoost,
          canUseTimeWarp: mutexStatus.canUseTimeWarp,
          activeSpeedBoost: mutexStatus.activeSpeedBoost ? {
            id: mutexStatus.activeSpeedBoost.id,
            type: mutexStatus.activeSpeedBoost.type,
            multiplier: mutexStatus.activeSpeedBoost.multiplier,
            endTime: mutexStatus.activeSpeedBoost.endTime,
            remainingTime: BoosterMutexService.formatRemainingTime(mutexStatus.activeSpeedBoost.endTime)
          } : null,
          activeTimeWarp: mutexStatus.activeTimeWarp ? {
            id: mutexStatus.activeTimeWarp.id,
            type: mutexStatus.activeTimeWarp.type,
            multiplier: mutexStatus.activeTimeWarp.multiplier,
            endTime: mutexStatus.activeTimeWarp.endTime,
            remainingTime: BoosterMutexService.formatRemainingTime(mutexStatus.activeTimeWarp.endTime)
          } : null
        }
      });
    } catch (error) {
      console.error('Get booster mutex status error:', error);
      res.status(500).json({ ok: false, message: tFromRequest(req, 'errors.serverError') });
    }
  }

  // 获取购买历史
  async getPurchaseHistory(req: MyRequest, res: Response) {
    try {
      const walletId = Number(req.user?.walletId);
      const { page = 1, limit = 20 } = req.query;
      const pageNum = Number(page);
      const limitNum = Number(limit);

      if (!walletId) {
        return res.status(400).json({ ok: false, message: tFromRequest(req, 'errors.iap.walletIdRequired') });
      }

      const offset = (pageNum - 1) * limitNum;

      const { count, rows: purchases } = await IapPurchase.findAndCountAll({
        where: { walletId },
        include: [{
          model: IapProduct,
          attributes: ['name', 'type', 'description']
        }],
        order: [['createdAt', 'DESC']],
        limit: limitNum,
        offset
      });

      res.json({
        ok: true,
        purchases,
        pagination: {
          total: count,
          page: pageNum,
          limit: limitNum,
          totalPages: Math.ceil(count / limitNum)
        }
      });
    } catch (error) {
      console.error('Get purchase history error:', error);
      res.status(500).json({ ok: false, message: tFromRequest(req, 'errors.serverError') });
    }
  }

  // 清理过期的激活道具
  async cleanupExpiredBoosters() {
    try {
      const now = dayjs().toDate();
      const expiredCount = await ActiveBooster.destroy({
        where: {
          endTime: { [Op.lt]: now }
        }
      });

      console.log(`Cleaned up ${expiredCount} expired boosters`);
      return expiredCount;
    } catch (error) {
      console.error('Cleanup expired boosters error:', error);
      throw error;
    }
  }

  // 获取道具效果（用于游戏逻辑计算）
  async getBoosterEffects(walletId: number) {
    try {
      const activeBoosters = await ActiveBooster.findAll({
        where: {
          walletId,
          endTime: { [Op.gt]: dayjs().toDate() }
        }
      });

      const effects = {
        speedMultiplier: 1,
        hasTimeWarp: false
      };

      for (const booster of activeBoosters) {
        if (booster.type === 'speed_boost') {
          effects.speedMultiplier = Math.max(effects.speedMultiplier, booster.multiplier);
        } else if (booster.type === 'time_warp') {
          effects.hasTimeWarp = true;
        }
      }

      return effects;
    } catch (error) {
      console.error('Get booster effects error:', error);
      return { speedMultiplier: 1, hasTimeWarp: false };
    }
  }

  // 获取VIP效果（用于游戏逻辑计算）
  async getVipEffects(walletId: number) {
    try {
      const vipMembership = await VipMembership.findOne({
        where: { walletId }
      });

      if (!vipMembership || !vipMembership.checkAndUpdateStatus()) {
        return {
          isVip: false,
          deliverySpeedMultiplier: 1,
          blockPriceMultiplier: 1,
          productionSpeedMultiplier: 1
        };
      }

      return {
        isVip: true,
        deliverySpeedMultiplier: 1.3, // VIP 30% 出货线速度加成
        blockPriceMultiplier: 1.2,    // VIP 20% 出货线价格加成
        productionSpeedMultiplier: 1.3 // VIP 30% 牧场区生产速度加成
      };
    } catch (error) {
      console.error('Get VIP effects error:', error);
      return {
        isVip: false,
        deliverySpeedMultiplier: 1,
        blockPriceMultiplier: 1,
        productionSpeedMultiplier: 1
      };
    }
  }
}

export default new IapController();