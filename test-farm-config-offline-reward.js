// 测试基于farm_configs的累积离线奖励功能
const axios = require('axios');

const BASE_URL = 'http://localhost:3456/api';

// 测试用的钱包地址和签名（请替换为实际的测试数据）
const TEST_WALLET_ADDRESS = 'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t';
const TEST_SIGNATURE = 'test_signature';

async function testFarmConfigOfflineReward() {
    try {
        console.log('🧪 开始测试基于farm_configs的累积离线奖励功能...\n');

        // 1. 获取当前农场配置信息
        console.log('📊 获取当前农场配置信息...');
        try {
            const configResponse = await axios.get(`${BASE_URL}/admin/farm-config`, {
                headers: {
                    'wallet-address': TEST_WALLET_ADDRESS,
                    'wallet-signature': TEST_SIGNATURE
                }
            });
            
            console.log('当前激活的农场配置:');
            if (configResponse.data.data && configResponse.data.data.length > 0) {
                configResponse.data.data.slice(0, 5).forEach((config, index) => {
                    console.log(`- 等级 ${config.grade}: offline=${config.offline} GEM/秒`);
                });
                if (configResponse.data.data.length > 5) {
                    console.log(`- ... 还有 ${configResponse.data.data.length - 5} 个配置`);
                }
            }
            console.log('');
        } catch (configError) {
            console.log('⚠️  无法获取农场配置信息（可能需要管理员权限）');
            console.log('');
        }

        // 2. 获取用户农场区块信息
        console.log('🏡 获取用户农场区块信息...');
        try {
            const farmPlotsResponse = await axios.get(`${BASE_URL}/farm-plots`, {
                headers: {
                    'wallet-address': TEST_WALLET_ADDRESS,
                    'wallet-signature': TEST_SIGNATURE
                }
            });
            
            console.log('用户农场区块状态:');
            if (farmPlotsResponse.data.data && farmPlotsResponse.data.data.length > 0) {
                farmPlotsResponse.data.data.forEach(plot => {
                    if (plot.isUnlocked) {
                        console.log(`- 区块 ${plot.plotNumber}: 等级 ${plot.level}, 已解锁`);
                    }
                });
            }
            console.log('');
        } catch (farmError) {
            console.log('⚠️  无法获取农场区块信息');
            console.log('');
        }

        // 3. 获取累积离线奖励信息
        console.log('💎 获取累积离线奖励信息...');
        const getRewardResponse = await axios.get(`${BASE_URL}/wallet/offline-reward`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        console.log('当前累积离线奖励信息:');
        console.log('- 是否离线:', getRewardResponse.data.data.isOffline);
        console.log('- 离线时间:', getRewardResponse.data.data.offlineTime, '秒');
        console.log('- 累积离线奖励:', getRewardResponse.data.data.offlineReward.gem, 'GEM');
        console.log('- 牛奶生产量:', getRewardResponse.data.data.offlineReward.milkProduced);
        console.log('- 牛奶处理量:', getRewardResponse.data.data.offlineReward.milkProcessed);
        console.log('- 农场每秒产量:', getRewardResponse.data.data.offlineReward.farmProductionPerSecond);
        console.log('- 出货线每秒处理量:', getRewardResponse.data.data.offlineReward.deliveryProcessingPerSecond);
        console.log('- VIP状态:', getRewardResponse.data.data.offlineReward.hasVip);
        console.log('- 速度加成:', getRewardResponse.data.data.offlineReward.hasSpeedBoost);
        console.log('- 最后活跃时间:', getRewardResponse.data.data.lastActiveTime);
        console.log('');

        // 4. 等待一段时间，再次获取奖励信息，验证累积功能
        console.log('⏳ 等待5秒，然后再次获取奖励信息...');
        await new Promise(resolve => setTimeout(resolve, 5000));

        const getRewardResponse2 = await axios.get(`${BASE_URL}/wallet/offline-reward`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        console.log('5秒后的累积离线奖励信息:');
        console.log('- 是否离线:', getRewardResponse2.data.data.isOffline);
        console.log('- 离线时间:', getRewardResponse2.data.data.offlineTime, '秒');
        console.log('- 累积离线奖励:', getRewardResponse2.data.data.offlineReward.gem, 'GEM');
        console.log('- 牛奶生产量:', getRewardResponse2.data.data.offlineReward.milkProduced);
        console.log('- 牛奶处理量:', getRewardResponse2.data.data.offlineReward.milkProcessed);
        console.log('- 最后活跃时间:', getRewardResponse2.data.data.lastActiveTime);

        // 计算奖励增长
        const rewardIncrease = getRewardResponse2.data.data.offlineReward.gem - getRewardResponse.data.data.offlineReward.gem;
        console.log('- 奖励增长:', rewardIncrease.toFixed(3), 'GEM');
        console.log('');

        // 5. 如果有累积奖励，尝试领取
        if (getRewardResponse2.data.data.offlineReward.gem > 0) {
            console.log('💰 尝试领取累积离线奖励...');
            const claimResponse = await axios.post(`${BASE_URL}/wallet/claim-offline-reward`, {}, {
                headers: {
                    'wallet-address': TEST_WALLET_ADDRESS,
                    'wallet-signature': TEST_SIGNATURE
                }
            });

            console.log('领取结果:');
            console.log('- 已领取奖励:', claimResponse.data.data.claimedReward.gem, 'GEM');
            console.log('- 剩余奖励:', claimResponse.data.data.remainingReward.gem, 'GEM');
            console.log('- 当前GEM余额:', claimResponse.data.data.currentGem, 'GEM');
            console.log('');
        } else {
            console.log('⏰ 当前没有可领取的累积离线奖励');
            console.log('');
        }

        console.log('✅ 基于farm_configs的累积离线奖励功能测试完成！');
        console.log('📝 验证要点:');
        console.log('   - 奖励计算基于farm_configs表的offline字段');
        console.log('   - 奖励会持续累积，不会丢失');
        console.log('   - 支持灵活的查看和领取机制');

    } catch (error) {
        console.error('❌ 测试失败:', error.response?.data || error.message);
    }
}

// 运行测试
testFarmConfigOfflineReward();
