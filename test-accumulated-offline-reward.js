// 测试累积离线奖励功能
const axios = require('axios');

const BASE_URL = 'http://localhost:3456/api';

// 测试用的钱包地址和签名（请替换为实际的测试数据）
const TEST_WALLET_ADDRESS = 'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t';
const TEST_SIGNATURE = 'test_signature';

async function testAccumulatedOfflineReward() {
    try {
        console.log('🧪 开始测试累积离线奖励功能...\n');

        // 1. 首先获取当前的离线奖励信息
        console.log('📊 获取当前离线奖励信息...');
        const getRewardResponse = await axios.get(`${BASE_URL}/wallet/offline-reward`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        console.log('当前离线奖励信息:');
        console.log('- 是否离线:', getRewardResponse.data.data.isOffline);
        console.log('- 离线时间:', getRewardResponse.data.data.offlineTime, '秒');
        console.log('- 累积离线奖励:', getRewardResponse.data.data.accumulatedOfflineReward.gem, 'GEM');
        console.log('- 最后活跃时间:', getRewardResponse.data.data.lastActiveTime);
        console.log('- 最后计算时间:', getRewardResponse.data.data.lastCalculationTime);
        console.log('');

        // 2. 如果有累积奖励，尝试领取
        if (getRewardResponse.data.data.accumulatedOfflineReward.gem > 0) {
            console.log('💎 尝试领取累积离线奖励...');
            const claimResponse = await axios.post(`${BASE_URL}/wallet/claim-offline-reward`, {}, {
                headers: {
                    'wallet-address': TEST_WALLET_ADDRESS,
                    'wallet-signature': TEST_SIGNATURE
                }
            });

            console.log('领取结果:');
            console.log('- 已领取奖励:', claimResponse.data.data.claimedReward.gem, 'GEM');
            console.log('- 剩余奖励:', claimResponse.data.data.remainingReward.gem, 'GEM');
            console.log('- 当前GEM余额:', claimResponse.data.data.currentGem, 'GEM');
            console.log('');
        } else {
            console.log('⏰ 当前没有可领取的累积离线奖励');
            console.log('');
        }

        // 3. 再次获取离线奖励信息，验证累积功能
        console.log('🔄 再次获取离线奖励信息（验证累积功能）...');
        const getRewardResponse2 = await axios.get(`${BASE_URL}/wallet/offline-reward`, {
            headers: {
                'wallet-address': TEST_WALLET_ADDRESS,
                'wallet-signature': TEST_SIGNATURE
            }
        });

        console.log('更新后的离线奖励信息:');
        console.log('- 是否离线:', getRewardResponse2.data.data.isOffline);
        console.log('- 离线时间:', getRewardResponse2.data.data.offlineTime, '秒');
        console.log('- 累积离线奖励:', getRewardResponse2.data.data.accumulatedOfflineReward.gem, 'GEM');
        console.log('- 最后活跃时间:', getRewardResponse2.data.data.lastActiveTime);
        console.log('- 最后计算时间:', getRewardResponse2.data.data.lastCalculationTime);

        console.log('\n✅ 累积离线奖励功能测试完成！');

    } catch (error) {
        console.error('❌ 测试失败:', error.response?.data || error.message);
    }
}

// 运行测试
testAccumulatedOfflineReward();
