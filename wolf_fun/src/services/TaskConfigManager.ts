import { Transaction } from 'sequelize';
import { sequelize } from '../config/db';
import { TaskConfig } from '../models/TaskConfig';
import { TaskConfigVersion } from '../models/TaskConfigVersion';
import { TaskConfigLog } from '../models/TaskConfigLog';
import { ExcelConfigParser, TaskConfigData, ValidationResult } from './ExcelConfigParser';

export interface UploadResult {
  success: boolean;
  versionNumber: string;
  message: string;
  validation?: ValidationResult;
}

export interface ApplyResult {
  success: boolean;
  message: string;
  affectedTasks: number;
}

export interface RollbackResult {
  success: boolean;
  message: string;
  rolledBackToVersion: string;
}

export class TaskConfigManager {
  private parser: ExcelConfigParser;

  constructor() {
    this.parser = new ExcelConfigParser();
  }

  /**
   * 上传配置文件并创建新版本
   * @param filePath Excel文件路径
   * @param adminId 管理员ID
   * @param description 版本描述
   * @returns 上传结果
   */
  public async uploadConfigFromFile(
    filePath: string, 
    adminId: string, 
    description?: string
  ): Promise<UploadResult> {
    const transaction = await sequelize.transaction();
    
    try {
      // 解析Excel文件
      const configData = this.parser.parseExcelFile(filePath);
      
      // 验证配置数据
      const validation = this.parser.validateTaskConfigs(configData);
      
      if (!validation.isValid) {
        await TaskConfigLog.create({
          action: 'upload',
          adminId,
          status: 'failed',
          errorMessage: `配置验证失败: ${validation.errors.join('; ')}`,
          details: `尝试上传配置文件，包含${configData.length}条任务配置`
        }, { transaction });
        
        await transaction.commit();
        
        return {
          success: false,
          versionNumber: '',
          message: '配置验证失败',
          validation
        };
      }
      
      // 生成版本号
      const versionNumber = `v${Date.now()}`;
      
      // 创建配置版本
      await TaskConfigVersion.create({
        versionNumber,
        description: description || '配置更新',
        configData: JSON.stringify(configData),
        createdBy: adminId,
        isActive: false
      }, { transaction });
      
      // 记录成功日志
      await TaskConfigLog.create({
        action: 'upload',
        versionNumber,
        adminId,
        status: 'success',
        details: `成功上传${configData.length}条任务配置`
      }, { transaction });
      
      await transaction.commit();
      
      return {
        success: true,
        versionNumber,
        message: `成功上传配置，版本号: ${versionNumber}`,
        validation
      };
      
    } catch (error: any) {
      await transaction.rollback();

      // 记录失败日志
      await TaskConfigLog.create({
        action: 'upload',
        adminId,
        status: 'failed',
        errorMessage: error.message,
        details: '上传配置文件失败'
      });

      throw new Error(`上传配置失败: ${error.message}`);
    }
  }

  /**
   * 上传配置数据并创建新版本
   * @param configData 配置数据
   * @param adminId 管理员ID
   * @param description 版本描述
   * @returns 上传结果
   */
  public async uploadConfigData(
    configData: TaskConfigData[], 
    adminId: string, 
    description?: string
  ): Promise<UploadResult> {
    const transaction = await sequelize.transaction();
    
    try {
      // 验证配置数据
      const validation = this.parser.validateTaskConfigs(configData);
      
      if (!validation.isValid) {
        await TaskConfigLog.create({
          action: 'upload',
          adminId,
          status: 'failed',
          errorMessage: `配置验证失败: ${validation.errors.join('; ')}`,
          details: `尝试上传配置数据，包含${configData.length}条任务配置`
        }, { transaction });
        
        await transaction.commit();
        
        return {
          success: false,
          versionNumber: '',
          message: '配置验证失败',
          validation
        };
      }
      
      // 生成版本号
      const versionNumber = `v${Date.now()}`;
      
      // 创建配置版本
      await TaskConfigVersion.create({
        versionNumber,
        description: description || '配置更新',
        configData: JSON.stringify(configData),
        createdBy: adminId,
        isActive: false
      }, { transaction });
      
      // 记录成功日志
      await TaskConfigLog.create({
        action: 'upload',
        versionNumber,
        adminId,
        status: 'success',
        details: `成功上传${configData.length}条任务配置`
      }, { transaction });
      
      await transaction.commit();
      
      return {
        success: true,
        versionNumber,
        message: `成功上传配置，版本号: ${versionNumber}`,
        validation
      };
      
    } catch (error: any) {
      await transaction.rollback();

      // 记录失败日志
      await TaskConfigLog.create({
        action: 'upload',
        adminId,
        status: 'failed',
        errorMessage: error.message,
        details: '上传配置数据失败'
      });

      throw new Error(`上传配置失败: ${error.message}`);
    }
  }

  /**
   * 应用指定版本的配置
   * @param versionNumber 版本号
   * @param adminId 管理员ID
   * @returns 应用结果
   */
  public async applyConfig(versionNumber: string, adminId: string): Promise<ApplyResult> {
    const transaction = await sequelize.transaction();
    
    try {
      // 查找指定版本
      const version = await TaskConfigVersion.findOne({ 
        where: { versionNumber },
        transaction
      });
      
      if (!version) {
        throw new Error(`版本 ${versionNumber} 不存在`);
      }
      
      // 解析配置数据
      const configData = JSON.parse(version.configData) as TaskConfigData[];
      
      // 清空现有配置
      await TaskConfig.destroy({ 
        where: {},
        transaction 
      });
      
      // 批量创建新配置
      await TaskConfig.bulkCreate(configData.map(config => ({
        ...config,
        isActive: true
      })), { transaction });
      
      // 停用所有版本
      await TaskConfigVersion.update(
        { isActive: false },
        { where: {}, transaction }
      );
      
      // 激活当前版本
      await TaskConfigVersion.update(
        { isActive: true },
        { where: { versionNumber }, transaction }
      );
      
      // 记录成功日志
      await TaskConfigLog.create({
        action: 'apply',
        versionNumber,
        adminId,
        status: 'success',
        details: `成功应用配置版本 ${versionNumber}，包含${configData.length}条任务配置`
      }, { transaction });
      
      await transaction.commit();
      
      // 异步初始化所有用户的任务状态
      this.initializeAllUserTasks().catch(error => {
        console.error('初始化用户任务失败:', error);
      });
      
      return {
        success: true,
        message: `成功应用配置版本 ${versionNumber}`,
        affectedTasks: configData.length
      };
      
    } catch (error: any) {
      await transaction.rollback();

      // 记录失败日志
      await TaskConfigLog.create({
        action: 'apply',
        versionNumber,
        adminId,
        status: 'failed',
        errorMessage: error.message,
        details: `尝试应用配置版本 ${versionNumber}`
      });

      throw new Error(`应用配置失败: ${error.message}`);
    }
  }

  /**
   * 回滚到指定版本
   * @param versionNumber 目标版本号
   * @param adminId 管理员ID
   * @returns 回滚结果
   */
  public async rollbackToVersion(versionNumber: string, adminId: string): Promise<RollbackResult> {
    try {
      const result = await this.applyConfig(versionNumber, adminId);
      
      // 记录回滚日志
      await TaskConfigLog.create({
        action: 'rollback',
        versionNumber,
        adminId,
        status: 'success',
        details: `成功回滚到配置版本 ${versionNumber}`
      });
      
      return {
        success: true,
        message: `成功回滚到版本 ${versionNumber}`,
        rolledBackToVersion: versionNumber
      };
      
    } catch (error: any) {
      // 记录回滚失败日志
      await TaskConfigLog.create({
        action: 'rollback',
        versionNumber,
        adminId,
        status: 'failed',
        errorMessage: error.message,
        details: `尝试回滚到配置版本 ${versionNumber}`
      });

      throw new Error(`回滚配置失败: ${error.message}`);
    }
  }

  /**
   * 获取所有配置版本
   */
  public async getAllVersions(): Promise<TaskConfigVersion[]> {
    return TaskConfigVersion.findAll({
      order: [['createdAt', 'DESC']]
    });
  }

  /**
   * 获取当前活跃版本
   */
  public async getActiveVersion(): Promise<TaskConfigVersion | null> {
    return TaskConfigVersion.findOne({
      where: { isActive: true }
    });
  }

  /**
   * 获取配置操作日志
   */
  public async getConfigLogs(limit: number = 50): Promise<TaskConfigLog[]> {
    return TaskConfigLog.findAll({
      order: [['createdAt', 'DESC']],
      limit
    });
  }

  /**
   * 验证配置文件
   * @param filePath Excel文件路径
   * @param adminId 管理员ID
   * @returns 验证结果
   */
  public async validateConfigFile(filePath: string, adminId: string): Promise<ValidationResult> {
    try {
      const configData = this.parser.parseExcelFile(filePath);
      const validation = this.parser.validateTaskConfigs(configData);
      
      // 记录验证日志
      await TaskConfigLog.create({
        action: 'validate',
        adminId,
        status: validation.isValid ? 'success' : 'failed',
        details: `验证配置文件，包含${configData.length}条任务配置`,
        errorMessage: validation.isValid ? undefined : validation.errors.join('; ')
      });
      
      return validation;
      
    } catch (error: any) {
      // 记录验证失败日志
      await TaskConfigLog.create({
        action: 'validate',
        adminId,
        status: 'failed',
        errorMessage: error.message,
        details: '验证配置文件失败'
      });

      throw new Error(`验证配置文件失败: ${error.message}`);
    }
  }

  /**
   * 异步初始化所有用户的任务状态
   * 这个方法会在应用新配置后调用
   */
  private async initializeAllUserTasks(): Promise<void> {
    // 这里需要调用TaskService的方法来初始化所有用户的任务
    // 由于避免循环依赖，这里先留空，在TaskService中实现
    console.log('开始异步初始化所有用户的任务状态...');
  }
}
