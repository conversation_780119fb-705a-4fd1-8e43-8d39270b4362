import { Router } from 'express';
import { languageMiddleware } from '../middlewares/languageMiddleware';
import {
  uploadConfig,
  validateConfig,
  applyConfig,
  rollbackConfig,
  getVersions,
  getActiveVersion,
  getConfigLogs,
  getCurrentConfigs,
  initializeAllUserTasks
} from '../controllers/AdminTaskController';

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 简单的管理员认证中间件（实际项目中应该使用更严格的认证）
const adminAuthMiddleware = (req: any, res: any, next: any) => {
  // 这里可以添加管理员认证逻辑
  // 例如检查JWT token中的角色，或者检查特定的API key
  const adminKey = req.headers['x-admin-key'];
  
  // 开发环境下允许通过，生产环境需要严格验证
  if (process.env.NODE_ENV === 'development' || adminKey === process.env.ADMIN_API_KEY) {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: '无权限访问管理接口'
    });
  }
};

// 应用管理员认证中间件
router.use(adminAuthMiddleware);

/**
 * 上传Excel配置文件
 * POST /api/admin/tasks/upload
 * Content-Type: multipart/form-data
 * Body:
 * - configFile: Excel文件
 * - adminId: 管理员ID
 * - description: 版本描述
 */
router.post('/upload', uploadConfig);

/**
 * 验证Excel配置文件
 * POST /api/admin/tasks/validate
 * Content-Type: multipart/form-data
 * Body:
 * - configFile: Excel文件
 * - adminId: 管理员ID
 */
router.post('/validate', validateConfig);

/**
 * 应用指定版本的配置
 * POST /api/admin/tasks/apply/:versionNumber
 * Body:
 * - adminId: 管理员ID
 */
router.post('/apply/:versionNumber', applyConfig);

/**
 * 回滚到指定版本
 * POST /api/admin/tasks/rollback/:versionNumber
 * Body:
 * - adminId: 管理员ID
 */
router.post('/rollback/:versionNumber', rollbackConfig);

/**
 * 获取所有配置版本
 * GET /api/admin/tasks/versions
 */
router.get('/versions', getVersions);

/**
 * 获取当前活跃版本
 * GET /api/admin/tasks/active-version
 */
router.get('/active-version', getActiveVersion);

/**
 * 获取配置操作日志
 * GET /api/admin/tasks/logs
 * Query参数:
 * - limit: number - 返回记录数量限制，默认50
 */
router.get('/logs', getConfigLogs);

/**
 * 获取当前任务配置
 * GET /api/admin/tasks/configs
 */
router.get('/configs', getCurrentConfigs);

/**
 * 初始化所有用户任务
 * POST /api/admin/tasks/initialize-all-users
 */
router.post('/initialize-all-users', initializeAllUserTasks);

export default router;
