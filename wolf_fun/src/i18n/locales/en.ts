// src/i18n/locales/en.ts
export default {
  iap: {
    productNotFound: 'Product not found',
    purchaseSuccess: 'Purchase completed successfully',
    paymentFailed: 'Payment failed',
    dailyTypeLimitReached: 'Daily purchase limit reached for %{productType} products. You can only purchase one %{productType} product per day.',
    dailyLimitReached: 'Daily purchase limit reached for product: %{productName}',
    accountLimitReached: 'Account purchase limit reached for product: %{productName}',
    vipAlreadyActive: 'VIP membership already active',
    priceNotAvailable: 'Price not available for selected payment method for product: %{productName}',
    walletIdRequired: 'Wallet ID is required',
    missingRequiredParameters: 'Missing required parameters',
    walletNotFound: 'Wallet not found',
    dappPortalConfigMissing: 'DappPortal configuration missing',
    userWalletAddressNotFound: 'User wallet address not found',
    failedToCreatePaymentOrder: 'Failed to create payment order',
    dappPortalUnavailable: 'DappPortal service unavailable',
    invalidPaymentResponse: 'Invalid response from payment service',
    walletIdAndBoosterIdRequired: 'Wallet ID and Booster ID are required',
    boosterNotFoundOrInsufficient: 'Booster not found or insufficient quantity',
    sameTypeBoosterAlreadyActive: 'Same type booster is already active',
    speedBoostAlreadyActive: 'A speed boost is already active with {{remainingTime}} remaining. Please wait for the current boost to end before using a new one.',
    boosterMutexConflict: 'Cannot use this booster due to conflicting active boosters',
    products: {
      speed_boost_x2_1hr: {
        description: 'Double your mining speed for 1 hour. Perfect for quick progress boosts!'
      },
      speed_boost_x2_24hr: {
        description: 'Double your mining speed for 24 hours. Maximize your daily earnings!'
      },
      speed_boost_x4_1hr: {
        description: 'Quadruple your mining speed for 1 hour. Ultimate power boost for rapid gains!'
      },
      speed_boost_x4_24hr: {
        description: 'Quadruple your mining speed for 24 hours. The ultimate daily advantage!'
      },
      time_warp_1hr: {
        description: 'Skip 1 hour of waiting time instantly. Get your rewards now!'
      },
      time_warp_24hr: {
        description: 'Skip 24 hours of waiting time instantly. Jump ahead a full day!'
      },
      vip_membership: {
        description: 'Unlock exclusive VIP benefits: higher limits, special rewards, and premium features!'
      },
      special_offer_bundle: {
        description: 'Limited time bundle with amazing value! Includes multiple boosters and exclusive items.'
      },
      // Generic product type descriptions
      speed_boost: {
        description: 'Boost your mining speed for faster progress and higher earnings!'
      },
      time_warp: {
        description: 'Skip waiting time instantly and get your rewards now!'
      },
      special_offer: {
        description: 'Special limited-time offer with amazing value!'
      }
    }
  },
  errors: {
    auth: {
      invalidToken: 'Invalid or expired token',
      unauthorized: 'Unauthorized access',
      userNotFound: 'User not found',
      invalidCredentials: 'Invalid credentials',
      tokenExpired: 'Token has expired',
      insufficientPermissions: 'Insufficient permissions'
    },
    iap: {
      dailyTypeLimitReached: 'Daily purchase limit reached for %{productType} products. You can only purchase one %{productType} product per day.',
      dailyLimitReached: 'Daily purchase limit reached for product: %{productName}',
      accountLimitReached: 'Account purchase limit reached for product: %{productName}',
      vipAlreadyActive: 'VIP membership already active',
      priceNotAvailable: 'Price not available for selected payment method for product: %{productName}',
      walletIdRequired: 'Wallet ID is required',
      missingRequiredParameters: 'Missing required parameters',
      productNotFound: 'Product not found or inactive',
      walletNotFound: 'Wallet not found',
      dappPortalConfigMissing: 'DappPortal configuration missing',
      userWalletAddressNotFound: 'User wallet address not found',
      failedToCreatePaymentOrder: 'Failed to create payment order',
      dappPortalUnavailable: 'DappPortal service unavailable',
      invalidPaymentResponse: 'Invalid response from payment service',
      walletIdAndBoosterIdRequired: 'Wallet ID and Booster ID are required',
      boosterNotFoundOrInsufficient: 'Booster not found or insufficient quantity',
      sameTypeBoosterAlreadyActive: 'Same type booster is already active'
    },
    unauthorized: "Unauthorized",
    insufficientFragments: "Insufficient fragments",
    invalidFragmentType: "Invalid fragment type",
    noToken: "No token provided",
    noTokenProvided: "No token provided",
    invalidTokenFormat: "Invalid token format",
    paramValidation: "Parameter validation failed",
    walletBound: "Wallet address already bound with other account",
    singleWallet: "Each TG account can only bind one wallet",
    uniqueCodeGeneration: "Failed to generate a unique invite code, please retry",
    unknown: "An unknown error occurred",
    invalidLimit: "Invalid limit value",
    invalidFields: "Please provide fields and amounts to add in format: %{fieldName: amount}",
    invalidFieldNames: "Invalid fields: %{fields}, valid fields are: %{validFields}",
    invalidFieldValues: "The following fields have invalid values: %{fields}, please ensure all values are numbers greater than 0",
    noValidFieldsToUpdate: "No valid fields to update",
    missingInitData: "Missing initData",
    invalidTelegramUserId: "Invalid Telegram user ID",
    invalidTelegramData: "Invalid Telegram data",
    userDataNotFound: "User data not found",
    authenticationFailed: "Authentication failed",
    sourceUserNotFound: "Source user not found",
    invitationCodeNotExist: "Invitation code does not exist",
    userNotFound: "User not found",
    notEnoughChests: "Not enough chests to open, required: %{required}, available: %{available}",
    chestOpenCountInvalid: "Chest open count can only be 1 or 10, got %{count}",
    alreadyHaveReferrer: "You already have a referrer. Cannot bind again.",
    walletAlreadyHasReferrer: "This wallet already has a referrer. Cannot bind again.",
    cannotUseOwnCode: "Cannot use your own code as referrer",
    cannotUseOwnWalletCode: "Cannot use your own wallet code as referrer",
    taskNotFound: "Task not found",
    taskOnlyOnce: "This task can only be completed once",
    taskAlreadyCompleted: "You have already completed today's task.",
    getTaskListFailed: "Failed to get task list",
    completeTaskFailed: "Failed to complete task",
    quantityMustBePositive: "Quantity must be positive",
    insufficientBalance: "Insufficient balance, unable to purchase tickets.",
    alreadyClaimedToday: "Already claimed today",
    notEnoughInvites: "Not enough invites to claim daily box",
    apiKeyRequired: "TONCENTER_API_KEY is required",
    invalidPayloadLength: "Invalid payload length, got %{length}, expected 32",
    invalidPayloadSignature: "Invalid payload signature",
    payloadExpired: "Payload has expired",
    tonProofExpired: "TON proof has expired",
    proofTimestampTooOld: "Proof timestamp is too old",
    checkProofErrorPublicKeyNotFound: "CheckProof error: public key not found",
    checkProofErrorPublicKeyMismatch: "CheckProof error: public key mismatch",
    checkProofErrorAddressMismatch: "CheckProof error: address mismatch",
    domainLengthMismatch: "Domain length mismatched against provided length bytes of %{lengthBytes}",
    roomLockFailed: "Failed to acquire room allocation lock, please try again later",
    getRoomIdFailed: "Failed to get current room ID: %{error}",
    luaScriptFailed: "Failed to execute Lua script: %{error}",
    roomExceedsLimit: "Room %{roomId} exceeds limit: %{count}",
    roomAllocationFailed: "Room allocation failed",
    roomFull: "Room %{roomId} is full",
    walletInfoNotFound: "Wallet information not found",
    withdrawalAmountEmpty: "Withdrawal amount cannot be empty",
    withdrawalAddressNotFound: "No valid wallet address found, please provide a withdrawal address",
    getPublicKeyNotImplemented: "Get public key not implemented",
    publicKeyNotMatch: "Public key does not match",
    stateInitNotMatch: "State init does not match",
    timestampNotMatch: "Timestamp does not match",
    noRewardsToCollect: "No rewards to collect",
    userWalletNotFound: "User wallet not found",
    noBullKingRewards: "No Bull King rewards to claim",
    notCompletedThreeRounds: "You haven't completed 3 rounds today, cannot claim rebate",
    withdrawalMinAmount: "Withdrawal amount cannot be less than %{minAmount} %{currency}",
    withdrawalDailyLimitReached: "Daily withdrawal limit of %{dailyLimit} times reached",
    insufficientFundsWithFee: "Insufficient funds, need %{totalAmount} %{currency} (including %{fee} %{currency} fee)",
    insufficientUnlockedMOOF: "Insufficient unlocked MOOF balance, need %{totalAmount} MOOF (including %{fee} MOOF fee)",
    userNotExist: "User does not exist",
    invalidEmailFormat: "Invalid email format",
    emailAlreadyBound: "This email is already bound to another user",
    receiverWalletAddressEmpty: "Receiver wallet address cannot be empty",
    transferAmountMustBePositive: "Transfer amount must be greater than 0",
    senderWalletNotExist: "Sender wallet does not exist",
    receiverWalletNotExist: "Receiver wallet does not exist",
    cannotTransferToSelf: "Cannot transfer to yourself",
    transferFailedInsufficientBalance: "Transfer failed, possibly due to insufficient balance",
    insufficientFreeTickets: "Insufficient free tickets",
    freeTicketAmountMustBePositiveInteger: "Free ticket amount must be a positive integer",
    freeTicketDailyLimitReached: "Daily free ticket transfer limit reached. Limit: %{dailyLimit}, Remaining: %{remaining}",
    transferFailedInsufficientFreeTickets: "Transfer failed, insufficient free tickets",
    transferFreeTicketFailed: "Failed to transfer free tickets",
    refundFailed: "Refund operation failed, wallet: %{walletId}",
    multipleRefundsFailed: "One or more refund operations failed",
    winnerOperationFailed: "Winner operation failed, wallet: %{walletId}",
    loserOperationFailed: "Loser operation failed, wallet: %{walletId}",
    multipleLoserOperationsFailed: "One or more loser operations failed",
    invalidTaskData: "Invalid task data",
    winnerNotFound: "Winner not found",
    getKolRewardsFailed: "Failed to get KOL rewards",
    claimKolRewardFailed: "Failed to claim KOL reward",
    getKolStatusFailed: "Failed to get KOL status",
    rewardNotFoundOrClaimed: "Reward not found or already claimed",
    getBullKingLeaderboardFailed: "Failed to get Bull King leaderboard",
    getMoofHoldersLeaderboardFailed: "Failed to get MOOF holders leaderboard",
    // Web3钱包登录相关错误
    invalidSignature: "Invalid wallet signature",
    invalidNonce: "Invalid or expired nonce",
    loginFailed: "Web3 wallet login failed",
    failedToGenerateUniqueCode: "Failed to generate a unique invitation code",
    invalidAccelerationSeconds: "Please provide valid acceleration seconds",
    accelerationExceedsLimit: "Single acceleration cannot exceed 10 seconds",
    accelerationLimitExceeded: "Daily acceleration time limit reached",
    countdownNotFound: "Countdown record not found",
    countdownNotActive: "Chest countdown is not active or has ended, cannot accelerate",
    claimMoofHoldersRewardFailed: "Failed to claim MOOF holders reward",
    claimBullKingRewardFailed: "Failed to claim Bull King reward",
    getPersonalKolLeaderboardFailed: "Failed to get Personal KOL leaderboard",
    claimPersonalKolRewardFailed: "Failed to claim Personal KOL reward",
    getTeamKolLeaderboardFailed: "Failed to get Team KOL leaderboard",
    claimTeamKolRewardFailed: "Failed to claim Team KOL reward",
    getPersonalKolProgressFailed: "Failed to get Personal KOL progress",
    getTeamKolProgressFailed: "Failed to get Team KOL progress",
    getDailyPromotionProgressFailed: "Failed to get daily promotion progress",
    invalidPagination: "Invalid pagination parameters",
    missingWalletId: "Missing wallet ID",
    missingUserId: "Missing user ID",
    missingUserOrWalletId: "Missing user ID or wallet ID",
    serverError: "Internal server error",
    roomDetailsNotFound: "Room details not found",
    getDailyRebateDetailsFailed: "Failed to get daily rebate details",
    getPendingRebateAmountFailed: "Failed to get pending rebate amount",
    requestInProgress: "You have another request in progress, please try again later",
    noSessionsProvided: "Please submit at least one session",
    invalidSessionFormat: "Each session must contain session_dt, session_category and at least one round",
    sessionNotFound: "Session %{category} %{date} does not exist",
    insufficientBalanceForReservation: "Insufficient ticket or USD balance to reserve all sessions",
    databaseConnectionError: "Database connection error",
    invalidDateFormat: "Invalid date format, please use %{format}",
    invalidDate: "Invalid date",
    dateNotToday: "Date must be today",
    userInfoFailed: "Failed to get user information",
    sendEmailCodeFailed: "Failed to send email verification code",
    bindEmailFailed: "Failed to bind email",
    verificationCodeExpired: "Verification code has expired",
    incorrectVerificationCode: "Incorrect verification code",
    emailNotBound: "You have not bound an email yet, please bind an email first",
    sendTransferCodeFailed: "Failed to send transfer verification code",
    transferFailed: "Transfer failed",
    getWalletHistoryFailed: "Failed to get wallet history",
    getWithdrawalSettingsFailed: "Failed to get withdrawal settings",
    withdrawUSDFailed: "Failed to withdraw USD",
    withdrawMOOFFailed: "Failed to withdraw MOOF",
    withdrawTONFailed: "Failed to withdraw TON",
    getMoofBalancesFailed: "Failed to get MOOF balances",
    getChestRewardsFailed: "Failed to get chest rewards",
    openChestsFailed: "Failed to open chests",
    getBullUnlockHistoryFailed: "Failed to get MOOF unlock history",
    roomNotFound: "Room not found",
    getRoomDetailsFailed: "Failed to get room details",
    chestNotAvailableYet: "Chest is not available for collection yet",
    jackpotPoolNotFound: "Jackpot pool not found",
    shareLinkNotFound: "Share boost link not found",
    shareLinkExpired: "Share boost link has expired",
    shareLinkMaxUsesReached: "Share boost link has reached maximum uses",
    cannotBoostYourself: "You cannot boost yourself",
    alreadyUsedShareLink: "You have already used this share boost link",
    missingChestId: "Missing chest ID",
    missingShareCode: "Missing share code",
    invalidAutoCollectValue: "Invalid auto collect value",
    noReferralChests: "No referral chests available",
    alreadyCollectedFourChests: "You have already collected the four chests bonus",
    pendingMilkAmountMustBeNonNegative: "Pending milk amount must be a non-negative number",
    gemAmountMustBePositive: "Gem amount must be a positive number",
    milkAmountMustBePositive: "Milk amount must be a positive number",
    invalidAmount: "Invalid amount",
    increaseMilkFailed: "Failed to increase pending milk",
    deliveryLineNotExist: "Delivery line does not exist",
    deliveryLineNotFound: "Delivery line not found",
    insufficientMilk: "Insufficient milk",
    notEnoughPendingMilk: "Not enough pending milk",
    increaseGemFailed: "Failed to increase gem",
    getOfflineRewardFailed: "Failed to get offline reward",
    notOffline: "Not offline",
    userNeverActive: "User has never been active",
    userStillOnline: "User is still online",
    claimOfflineRewardFailed: "Failed to claim offline reward",
    batchUpdateResourcesFailed: "Batch resource update failed",
    // Test reset related errors
    rateLimitExceeded: "Too many requests, please try again later",
    forbidden: "Insufficient permissions",
  },
  success: {
    ticketPurchase: "Ticket purchased successfully",
    fragmentCraft: "Fragment crafting successful",
    proofGeneration: "Proof generated successfully",
    iap: {
      boosterActivated: "%{boosterType} booster activated successfully"
    },
    claimMoofHoldersReward: "MOOF holders reward claimed successfully",
    claimBullKingReward: "Bull King reward claimed successfully",
    claimPersonalKolReward: "Personal KOL reward claimed successfully",
    claimTeamKolReward: "Team KOL reward claimed successfully",
    chestAccelerated: "Chest countdown accelerated successfully",
    getPersonalKolProgress: "Personal KOL progress retrieved successfully",
    getTeamKolProgress: "Team KOL progress retrieved successfully",
    getDailyPromotionProgress: "Daily promotion progress retrieved successfully",
    taskCompleted: "Task completed successfully",
    addTestCoinAllAccounts: "Successfully added the following fields to all accounts: %{fields}",
    addTestCoinYourAccount: "Successfully added the following fields to your account: %{fields}",
    getBullUnlockHistory: "MOOF unlock history retrieved successfully",
    chestOpened: "Chest opened successfully",
    chestCount: "Chest count retrieved successfully",
    dailyChestsClaimed: "Successfully claimed %{count} daily chests",
    getGameSessionRanking: "Game session ranking retrieved successfully",
    getGameSessionLeaderboard: "Game session leaderboard retrieved successfully",
    craftTicket: "Ticket crafted successfully",
    transferFreeTicketSuccess: "Free tickets transferred successfully",
    getRemainingTransferLimit: "Remaining transfer limit retrieved successfully",
    getAllSessions: "All sessions retrieved successfully",
    getGameHistory: "Game history retrieved successfully",
    getRoomDetails: "Room details retrieved successfully",
    getPersonalKolStats: "Personal KOL statistics retrieved successfully",
    getTeamKolStats: "Team KOL statistics retrieved successfully",
    getPersonalKolHistory: "Personal KOL history retrieved successfully",
    getTeamKolHistory: "Team KOL history retrieved successfully",
    getMyReservations: "My reservations retrieved successfully",
    getDailyRebateDetails: "Daily rebate details retrieved successfully",
    getPendingRebateAmount: "Pending rebate amount retrieved successfully",
    claimRebateSuccess: "Rebate claimed successfully",
    referralBound: "Referral code bound successfully",
    referralList: "Referral list retrieved successfully",
    referralStatus: "Referral status retrieved successfully",
    reservationSuccess: "Reservation successful",
    getReservations: "Reservations retrieved successfully",
    getKolRewards: "KOL rewards retrieved successfully",
    claimKolReward: "KOL reward claimed successfully",
    getKolStatus: "KOL status retrieved successfully",
    emailCodeSent: "Email verification code sent successfully",
    emailBound: "Email bound successfully",
    transferCodeSent: "Transfer verification code sent successfully",
    transferSuccess: "Transfer completed successfully",
    getWalletHistory: "Wallet history retrieved successfully",
    getWithdrawalSettings: "Withdrawal settings retrieved successfully",
    withdrawUSDSuccess: "USD withdrawn successfully",
    withdrawMOOFSuccess: "MOOF withdrawn successfully",
    withdrawTONSuccess: "TON withdrawn successfully",
    getMoofBalancesSuccess: "MOOF balances retrieved successfully",
    getChestRewards: "Get chest rewards successfully",
    openChests: "Open chests successfully",
    increaseMilk: "Pending milk increased successfully",
    increaseGem: "Gem increased successfully",
    milkToGem: "Milk to Gem conversion successful",
    getOfflineReward: "Offline reward retrieved successfully",
    claimOfflineReward: "Offline reward claimed successfully",
    batchUpdateResources: "Batch resource update successful",
    // Test reset related success messages
    gameStateReset: "Game state reset successfully"
  },
  validation: {
    quantity: {
      positive: "Quantity must be a positive integer"
    }
  },
  referral: {
    dailyChestReward: "Daily claim%{count}mystery chests"
  },
  labels: {
    notQualified: "Not Qualified",
    oneStarKol: "One Star KOL",
    twoStarKol: "Two Star KOL",
    threeStarKol: "Three Star KOL",
    silverKol: "Silver KOL",
    goldKol: "Gold KOL",
    claimed: "Claimed",
    canClaim: "Can Claim"
  },
  tasks: {
    dailySignin: "Daily Reward Chest",
    joinTelegram: "Join our Telegram Channel Chest",
    followTwitter: "Follow our X Chest"
  },
  email: {
    verificationCode: {
      subject: "MoofFun Email Verification Code",
      title: "Email Verification",
      binding: "You are binding your email address. Please use the following verification code to complete the process:"
    },
    transfer: {
      subject: "MoofFun Transfer Verification Code",
      title: "Transfer Verification",
      operation: "You are initiating a transfer. Please verify the following details and use the verification code to confirm:",
      amount: "Transfer Amount",
      receiverAddress: "Receiver Address"
    },
    common: {
      greeting: "Dear User,",
      verificationCode: "Verification Code",
      codeValidity: "This verification code is valid for 5 minutes.",
      ignoreMessage: "If you did not request this operation, please ignore this email.",
      autoSendNotice: "This is an automated message, please do not reply."
    },
    rebate: {
      title: "Ticket Rebate Settlement Notice",
      content: "%{count} ticket rebates have been settled, total %{amount} USD has been added to your account.",
      displayRemark: "Claim Ticket Rebate",
      developerRemark: "Claimed %{count} pending ticket rebates"
    }
  },
  walletHistory: {
    transferOut: "Transfer out %{amount} USD to %{address}",
    transferInDev: "Receive USD from %{address}",
    transferIn: "Receive %{amount} USD from %{address}",
    transferOutDev: "Transfer USD to %{address}",
    bet: "Bet",
    betSession: "Bet Session %{sessionNumber} ROUND %{roundIndex}",
    withdrawal: "Withdrawal",
    withdrawalUSD: "USD withdrawal to address %{address}",
    withdrawalUSDDev: "USD withdrawal, amount: %{amount}, fee: %{fee}, status: %{status}",
    withdrawalMOOF: "MOOF withdrawal to address %{address}",
    withdrawalMOOFDev: "MOOF withdrawal, amount: %{amount}, fee: %{fee}",
    withdrawalTON: "TON withdrawal to address %{address}",
    withdrawalTONDev: "TON withdrawal, amount: %{amount}, fee: %{fee}",
    transferFreeTicketOut: "Transfer out %{amount} free tickets to %{address}",
    transferFreeTicketIn: "Receive %{amount} free tickets from %{address}",
    transferFreeTicketOutDev: "Transfer free tickets to %{address}",
    transferFreeTicketInDev: "Receive free tickets from %{address}",
    reference: {
      increasePendingMilk: "Increase Pending Milk",
      increaseGem: "Increase Gem",
      convertMilkToGem: "Convert Milk to Gem",
      offlineReward: "Offline Reward"
    },
    feDisplayRemark: {
      increasedPendingMilk: "Increased %{amount} pending milk",
      increasedGem: "Increased %{amount} gem",
      milkConvertedToGem: "Converted %{amount} Milk to Gem",
      offlineGemReward: "Received %{amount} gem from offline reward"
    }
  },
  chest: {
    level1: "Level 1 Chest",
    level2: "Level 2 Chest",
    level3: "Level 3 Chest",
    level4: "Level 4 Rare Chest",
    rewards: {
      ticket: "Ticket",
      fragment_green: "Green Fragment",
    fragment_blue: "Blue Fragment",
    fragment_purple: "Purple Fragment",
    fragment_gold: "Gold Fragment",
      ton: "TON",
      gem: "GEM"
    },
    announcement: {
      title: "Rare Chest Reward!",
      content: "Congratulations to player ID: %{userId} for opening a Level 4 Rare Chest! Rewards: Ticket x1, TON x 0.1, Ticket Fragment x 5, GEM x 1000"
    },
    summary: {
      level1: "Opened %{count} Level 1 Chests",
      level2: "Opened %{count} Level 2 Chests",
      level3: "Opened %{count} Level 3 Chests",
      level4: "Opened %{count} Level 4 Chests"
    },
    error: {
      fieldNotExist: "Reward field does not exist in wallet: %{field}",
      updateFailed: "Failed to update wallet field: %{field}"
    }
  },
  jackpot: {
    announcement: {
      levelUp: {
        title: "Jackpot Chest Upgraded",
        content: "Congratulations! The Jackpot Chest has upgraded to Level %{level}, the pool amount of %{amount} TON has been claimed, now entering Level %{nextLevel}!"
      },
      winner: {
        title: "Jackpot Chest Grand Prize",
        content: "Congratulations to user %{userId} for winning the Level %{level} Jackpot Chest grand prize of %{amount} TON!"
      }
    }
  }
}